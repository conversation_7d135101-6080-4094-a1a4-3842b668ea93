//
//  Generated file. Do not edit.
//

// clang-format off

#include "generated_plugin_registrant.h"

#include <bifrost_client_flutter/bifrost_client_flutter_plugin.h>

void fl_register_plugins(FlPluginRegistry* registry) {
  g_autoptr(FlPluginRegistrar) bifrost_client_flutter_registrar =
      fl_plugin_registry_get_registrar_for_plugin(registry, "BifrostClientFlutterPlugin");
  bifrost_client_flutter_plugin_register_with_registrar(bifrost_client_flutter_registrar);
}
