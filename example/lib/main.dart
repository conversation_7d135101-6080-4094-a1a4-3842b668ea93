import 'package:flutter/material.dart';
import 'package:bifrost_client_flutter/bifrost_client_flutter.dart';
import 'package:bifrost_client_flutter/src/proto/gen/dart/api/common/v1/common.pb.dart'
    as common;
import 'package:bifrost_client_flutter/src/proto/gen/dart/api/bifrostgateway/v1/gateway.pb.dart'
    as bifrostgateway;

void main() => runApp(MyApp());

class MyApp extends StatefulWidget {
  @override
  _MyAppState createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  final _client = BifrostClient(
    socketHost: 'gateway.example.com',
    socketPort: 443,
    handshakeUrl: 'https://api.example.com/v1/handshake',
    transferUrl: 'https://api.example.com/v1/transfer',
    deviceType: common.DeviceType.DEVICE_TYPE_IOS,
    appid: common.Appid.APPID_CALORIE_SCAN,
    appVersion: '1.0.0',
  );

  @override
  void initState() {
    super.initState();
    _initClient();
  }

  Future<void> _initClient() async {
    await _client.connect();
    _client.onMessage = (payload) {
      print('Received message: ${payload.messageId}');
    };
  }

  Future<void> _sendTestMessage() async {
    final payload = bifrostgateway.TransferPayload()
      ..messageId = 'test_123'
      ..data = Uint8List.fromList(utf8.encode('Hello Bifrost'));

    await _client.sendTransferMessage(payload);
  }

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text('Bifrost Client Test')),
        body: Center(
          child: ElevatedButton(
            onPressed: _sendTestMessage,
            child: Text('Send Test Message'),
          ),
        ),
      ),
    );
  }
}
