#include "include/bifrost_client_flutter/bifrost_client_flutter_plugin_c_api.h"

#include <flutter/plugin_registrar_windows.h>

#include "bifrost_client_flutter_plugin.h"

void BifrostClientFlutterPluginCApiRegisterWithRegistrar(
    FlutterDesktopPluginRegistrarRef registrar) {
  bifrost_client_flutter::BifrostClientFlutterPlugin::RegisterWithRegistrar(
      flutter::PluginRegistrarManager::GetInstance()
          ->GetRegistrar<flutter::PluginRegistrarWindows>(registrar));
}
