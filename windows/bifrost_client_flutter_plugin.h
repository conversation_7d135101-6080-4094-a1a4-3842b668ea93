#ifndef FLUTTER_PLUGIN_BIFROST_CLIENT_FLUTTER_PLUGIN_H_
#define FLUTTER_PLUGIN_BIFROST_CLIENT_FLUTTER_PLUGIN_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>

#include <memory>

namespace bifrost_client_flutter {

class BifrostClientFlutterPlugin : public flutter::Plugin {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows *registrar);

  BifrostClientFlutterPlugin();

  virtual ~BifrostClientFlutterPlugin();

  // Disallow copy and assign.
  BifrostClientFlutterPlugin(const BifrostClientFlutterPlugin&) = delete;
  BifrostClientFlutterPlugin& operator=(const BifrostClientFlutterPlugin&) = delete;

  // Called when a method is called on this plugin's channel from Dart.
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue> &method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);
};

}  // namespace bifrost_client_flutter

#endif  // FLUTTER_PLUGIN_BIFROST_CLIENT_FLUTTER_PLUGIN_H_
