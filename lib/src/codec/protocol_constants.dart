/// Protocol version
const int protocolVersion = 1;

/// Packet header length in bytes
const int headerLength = 5;

const int protocolMagic = 0xc;

/// Command types (3 bits)
class CommandType {
  static const int exchangeKey = 0;
  static const int clientRequest = 1;
  static const int serverResponse = 2;
  static const int serverData = 3;
  static const int clientResponse = 4;
}

/// Compression types (3 bits)
class CompressType {
  static const int none = 0;
  static const int lz4 = 1;
}

/// Payload types (2 bits)
class PayloadType {
  static const int protobuf = 0;
}
