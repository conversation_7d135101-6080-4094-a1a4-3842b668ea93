import 'dart:typed_data';
import 'dart:convert';

/// 协议魔数字节长度
const int magicLength = 4;

/// Packet header 固定长度（不含 magic 和 Transfer 扩展字段）
const int baseHeaderLength = 1 + 1 + 3;

/// TransferPacket 额外字段长度（authKeyId + sequenceId）
const int transferExtraLength = 8 + 8;

/// Base packet class containing common header fields and payload.
abstract class Packet {
  final int magic;
  final int version;
  final int commandType;
  final int compressType;
  final int payloadType;
  final Uint8List payload;

  Packet({
    required this.magic,
    required this.version,
    required this.commandType,
    required this.compressType,
    required this.payloadType,
    required this.payload,
  });

  Uint8List encode();

  /// Decode base header from buffer
  static int _readPayloadLength(ByteData data, int offset) {
    return (data.getUint8(offset) << 16) |
        (data.getUint8(offset + 1) << 8) |
        data.getUint8(offset + 2);
  }
}

/// Handshake packet used for initial key exchange and setup.
class HandshakePacket extends Packet {
  HandshakePacket({
    required int magic,
    required int version,
    required int commandType,
    required int compressType,
    required int payloadType,
    required Uint8List payload,
  }) : super(
         magic: magic,
         version: version,
         commandType: commandType,
         compressType: compressType,
         payloadType: payloadType,
         payload: payload,
       );

  @override
  Uint8List encode() {
    final header = BytesBuilder();
    header.addByte((version & 0xFF));
    int flags =
        ((commandType & 0x07) << 5) |
        ((compressType & 0x07) << 2) |
        (payloadType & 0x03);
    header.addByte(flags);
    int len = payload.length;
    header.add([(len >> 16) & 0xFF, (len >> 8) & 0xFF, len & 0xFF]);
    return Uint8List.fromList([
      ...Uint8List(4)..buffer.asByteData().setUint32(0, magic),
      ...header.toBytes(),
      ...payload,
    ]);
  }

  static HandshakePacket? decodeStream(Uint8List buffer) {
    if (buffer.length < magicLength + baseHeaderLength) return null;
    final data = ByteData.sublistView(buffer);
    final magic = data.getUint32(0);
    final version = data.getUint8(4);
    final flags = data.getUint8(5);
    final commandType = (flags >> 5) & 0x07;
    final compressType = (flags >> 2) & 0x07;
    final payloadType = flags & 0x03;
    final payloadLength = Packet._readPayloadLength(data, 6);

    final totalLength = 4 + baseHeaderLength + payloadLength;
    if (buffer.length < totalLength) return null;

    final payload = buffer.sublist(9, totalLength);

    return HandshakePacket(
      magic: magic,
      version: version,
      commandType: commandType,
      compressType: compressType,
      payloadType: payloadType,
      payload: payload,
    );
  }
}

/// Transfer packet used for regular encrypted data transfer.
class TransferPacket extends Packet {
  final int authKeyId;
  final int sequenceId;

  TransferPacket({
    required int magic,
    required int version,
    required int commandType,
    required int compressType,
    required int payloadType,
    required Uint8List payload,
    required this.authKeyId,
    required this.sequenceId,
  }) : super(
         magic: magic,
         version: version,
         commandType: commandType,
         compressType: compressType,
         payloadType: payloadType,
         payload: payload,
       );

  @override
  Uint8List encode() {
    final header = BytesBuilder();
    header.addByte((version & 0xFF));
    int flags =
        ((commandType & 0x07) << 5) |
        ((compressType & 0x07) << 2) |
        (payloadType & 0x03);
    header.addByte(flags);
    int len = payload.length;
    header.add([(len >> 16) & 0xFF, (len >> 8) & 0xFF, len & 0xFF]);

    final authKeyIdBytes = ByteData(8)..setUint64(0, authKeyId);
    final sequenceIdBytes = ByteData(8)..setUint64(0, sequenceId);

    return Uint8List.fromList([
      ...Uint8List(4)..buffer.asByteData().setUint32(0, magic),
      ...header.toBytes(),
      ...authKeyIdBytes.buffer.asUint8List(),
      ...sequenceIdBytes.buffer.asUint8List(),
      ...payload,
    ]);
  }

  static TransferPacket? decodeStream(Uint8List buffer) {
    if (buffer.length < magicLength + baseHeaderLength + transferExtraLength) {
      return null;
    }

    final data = ByteData.sublistView(buffer);
    final magic = data.getUint32(0);
    final version = data.getUint8(4);
    final flags = data.getUint8(5);
    final commandType = (flags >> 5) & 0x07;
    final compressType = (flags >> 2) & 0x07;
    final payloadType = flags & 0x03;
    final payloadLength = Packet._readPayloadLength(data, 6);
    final totalLength =
        4 + baseHeaderLength + transferExtraLength + payloadLength;

    if (buffer.length < totalLength) return null;

    final authKeyId = data.getUint64(9);
    final sequenceId = data.getUint64(17);
    final payload = buffer.sublist(25, totalLength);

    return TransferPacket(
      magic: magic,
      version: version,
      commandType: commandType,
      compressType: compressType,
      payloadType: payloadType,
      authKeyId: authKeyId,
      sequenceId: sequenceId,
      payload: payload,
    );
  }
}
