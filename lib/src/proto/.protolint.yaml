---
# Lint directives.
lint:
  # Linter files to ignore.
  ignores:
    - id: MESSAGE_NAMES_UPPER_CAMEL_CASE
      files:
    - id: ENUM_NAMES_UPPER_CAMEL_CASE
      files:

  # Linter files to walk.
  files:
    # The specific files to exclude.
    exclude:
      # NOTE: UNIX paths will be properly accepted by both UNIX and Windows.
      - path/to/file

  # Linter directories to walk.
  directories:
    # The specific directories to exclude.
    exclude:
      # NOTE: UNIX paths will be properly accepted by both UNIX and Windows.
      - path/to/dir

  # Linter rules.
  # Run `protolint list` to see all available rules.
  rules:
    # Determines whether or not to include the default set of linters.
    no_default: true

    # Set the default to all linters. This option works the other way around as no_default does.
    # If you want to enable this option, delete the comment out below and no_default.
    # all_default: true

    # The specific linters to add.
    add:
      - FIELD_NAMES_LOWER_SNAKE_CASE
      - MESSAGE_NAMES_UPPER_CAMEL_CASE
      - MAX_LINE_LENGTH
      - SERVICE_NAMES_END_WITH
      - FIELD_NAMES_EXCLUDE_PREPOSITIONS
      - MESSAGE_NAMES_EXCLUDE_PREPOSITIONS
      - FILE_NAMES_LOWER_SNAKE_CASE
      - IMPORTS_SORTED
      - PACKAGE_NAME_LOWER_CASE
      - ORDER
      - PROTO3_FIELDS_AVOID_REQUIRED
      - PROTO3_GROUPS_AVOID
      - REPEATED_FIELD_NAMES_PLURALIZED
      - SYNTAX_CONSISTENT
      - RPC_NAMES_CASE
      - QUOTE_CONSISTENT

    # The specific linters to remove.
    remove:
      - RPC_NAMES_UPPER_CAMEL_CASE

  # Linter rules option.
  rules_option:
    # MAX_LINE_LENGTH rule option.
    max_line_length:
      # Enforces a maximum line length
      max_chars: 90
      # Specifies the character count for tab characters
      tab_chars: 2
    # SYNTAX_CONSISTENT rule option.
    syntax_consistent:
      # Default is proto3.
      version: proto3
