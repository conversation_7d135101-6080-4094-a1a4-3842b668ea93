// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/common/v1/common.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DeviceType int32

const (
	DeviceType_DEVICE_TYPE_UNSPECIFIED DeviceType = 0
	DeviceType_DEVICE_TYPE_IOS         DeviceType = 1
	DeviceType_DEVICE_TYPE_ANDROID     DeviceType = 2
	DeviceType_DEVICE_TYPE_MACOSX      DeviceType = 3
	DeviceType_DEVICE_TYPE_WINDOWS     DeviceType = 4
	DeviceType_DEVICE_TYPE_LINUX       DeviceType = 5
	DeviceType_DEVICE_TYPE_MACOSX_ARM  DeviceType = 6
	DeviceType_DEVICE_TYPE_WINDOWS_ARM DeviceType = 7
	DeviceType_DEVICE_TYPE_LINUX_ARM   DeviceType = 8
	DeviceType_DEVICE_TYPE_HARMONY     DeviceType = 9
)

// Enum value maps for DeviceType.
var (
	DeviceType_name = map[int32]string{
		0: "DEVICE_TYPE_UNSPECIFIED",
		1: "DEVICE_TYPE_IOS",
		2: "DEVICE_TYPE_ANDROID",
		3: "DEVICE_TYPE_MACOSX",
		4: "DEVICE_TYPE_WINDOWS",
		5: "DEVICE_TYPE_LINUX",
		6: "DEVICE_TYPE_MACOSX_ARM",
		7: "DEVICE_TYPE_WINDOWS_ARM",
		8: "DEVICE_TYPE_LINUX_ARM",
		9: "DEVICE_TYPE_HARMONY",
	}
	DeviceType_value = map[string]int32{
		"DEVICE_TYPE_UNSPECIFIED": 0,
		"DEVICE_TYPE_IOS":         1,
		"DEVICE_TYPE_ANDROID":     2,
		"DEVICE_TYPE_MACOSX":      3,
		"DEVICE_TYPE_WINDOWS":     4,
		"DEVICE_TYPE_LINUX":       5,
		"DEVICE_TYPE_MACOSX_ARM":  6,
		"DEVICE_TYPE_WINDOWS_ARM": 7,
		"DEVICE_TYPE_LINUX_ARM":   8,
		"DEVICE_TYPE_HARMONY":     9,
	}
)

func (x DeviceType) Enum() *DeviceType {
	p := new(DeviceType)
	*p = x
	return p
}

func (x DeviceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DeviceType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_common_v1_common_proto_enumTypes[0].Descriptor()
}

func (DeviceType) Type() protoreflect.EnumType {
	return &file_api_common_v1_common_proto_enumTypes[0]
}

func (x DeviceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DeviceType.Descriptor instead.
func (DeviceType) EnumDescriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{0}
}

type Appid int32

const (
	Appid_APPID_UNSPECIFIED  Appid = 0
	Appid_APPID_CALORIE_SCAN Appid = 1
)

// Enum value maps for Appid.
var (
	Appid_name = map[int32]string{
		0: "APPID_UNSPECIFIED",
		1: "APPID_CALORIE_SCAN",
	}
	Appid_value = map[string]int32{
		"APPID_UNSPECIFIED":  0,
		"APPID_CALORIE_SCAN": 1,
	}
)

func (x Appid) Enum() *Appid {
	p := new(Appid)
	*p = x
	return p
}

func (x Appid) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Appid) Descriptor() protoreflect.EnumDescriptor {
	return file_api_common_v1_common_proto_enumTypes[1].Descriptor()
}

func (Appid) Type() protoreflect.EnumType {
	return &file_api_common_v1_common_proto_enumTypes[1]
}

func (x Appid) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Appid.Descriptor instead.
func (Appid) EnumDescriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{1}
}

type ChatType int32

const (
	ChatType_CHAT_TYPE_UNSPECIFIED ChatType = 0
	ChatType_CHAT_TYPE_PRIVATE     ChatType = 1
	ChatType_CHAT_TYPE_GROUP       ChatType = 2
)

// Enum value maps for ChatType.
var (
	ChatType_name = map[int32]string{
		0: "CHAT_TYPE_UNSPECIFIED",
		1: "CHAT_TYPE_PRIVATE",
		2: "CHAT_TYPE_GROUP",
	}
	ChatType_value = map[string]int32{
		"CHAT_TYPE_UNSPECIFIED": 0,
		"CHAT_TYPE_PRIVATE":     1,
		"CHAT_TYPE_GROUP":       2,
	}
)

func (x ChatType) Enum() *ChatType {
	p := new(ChatType)
	*p = x
	return p
}

func (x ChatType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ChatType) Descriptor() protoreflect.EnumDescriptor {
	return file_api_common_v1_common_proto_enumTypes[2].Descriptor()
}

func (ChatType) Type() protoreflect.EnumType {
	return &file_api_common_v1_common_proto_enumTypes[2]
}

func (x ChatType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ChatType.Descriptor instead.
func (ChatType) EnumDescriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{2}
}

type Status int32

const (
	Status_STATUS_UNSPECIFIED         Status = 0
	Status_STATUS_OK                  Status = 1
	Status_STATUS_BAD_REQUEST         Status = 2
	Status_STATUS_UNAUTHORIZED        Status = 3
	Status_STATUS_FORBIDDEN           Status = 4
	Status_STATUS_NOT_FOUND           Status = 5
	Status_STATUS_CONFLICT            Status = 6
	Status_STATUS_INTERNAL_ERROR      Status = 7
	Status_STATUS_SERVICE_UNAVAILABLE Status = 8
)

// Enum value maps for Status.
var (
	Status_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "STATUS_OK",
		2: "STATUS_BAD_REQUEST",
		3: "STATUS_UNAUTHORIZED",
		4: "STATUS_FORBIDDEN",
		5: "STATUS_NOT_FOUND",
		6: "STATUS_CONFLICT",
		7: "STATUS_INTERNAL_ERROR",
		8: "STATUS_SERVICE_UNAVAILABLE",
	}
	Status_value = map[string]int32{
		"STATUS_UNSPECIFIED":         0,
		"STATUS_OK":                  1,
		"STATUS_BAD_REQUEST":         2,
		"STATUS_UNAUTHORIZED":        3,
		"STATUS_FORBIDDEN":           4,
		"STATUS_NOT_FOUND":           5,
		"STATUS_CONFLICT":            6,
		"STATUS_INTERNAL_ERROR":      7,
		"STATUS_SERVICE_UNAVAILABLE": 8,
	}
)

func (x Status) Enum() *Status {
	p := new(Status)
	*p = x
	return p
}

func (x Status) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Status) Descriptor() protoreflect.EnumDescriptor {
	return file_api_common_v1_common_proto_enumTypes[3].Descriptor()
}

func (Status) Type() protoreflect.EnumType {
	return &file_api_common_v1_common_proto_enumTypes[3]
}

func (x Status) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Status.Descriptor instead.
func (Status) EnumDescriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{3}
}

type CommandID int32

const (
	CommandID_COMMAND_ID_UNSPECIFIED                 CommandID = 0
	CommandID_COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR CommandID = 1
	CommandID_COMMAND_ID_HEARTBEAT                   CommandID = 2
	CommandID_COMMAND_ID_TOO_MANY_REQUEST            CommandID = 3
)

// Enum value maps for CommandID.
var (
	CommandID_name = map[int32]string{
		0: "COMMAND_ID_UNSPECIFIED",
		1: "COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR",
		2: "COMMAND_ID_HEARTBEAT",
		3: "COMMAND_ID_TOO_MANY_REQUEST",
	}
	CommandID_value = map[string]int32{
		"COMMAND_ID_UNSPECIFIED":                 0,
		"COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR": 1,
		"COMMAND_ID_HEARTBEAT":                   2,
		"COMMAND_ID_TOO_MANY_REQUEST":            3,
	}
)

func (x CommandID) Enum() *CommandID {
	p := new(CommandID)
	*p = x
	return p
}

func (x CommandID) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommandID) Descriptor() protoreflect.EnumDescriptor {
	return file_api_common_v1_common_proto_enumTypes[4].Descriptor()
}

func (CommandID) Type() protoreflect.EnumType {
	return &file_api_common_v1_common_proto_enumTypes[4]
}

func (x CommandID) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommandID.Descriptor instead.
func (CommandID) EnumDescriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{4}
}

type ErrorDetail struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ErrorCode     int32                  `protobuf:"varint,1,opt,name=error_code,json=errorCode,proto3" json:"error_code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ErrorDetail) Reset() {
	*x = ErrorDetail{}
	mi := &file_api_common_v1_common_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ErrorDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ErrorDetail) ProtoMessage() {}

func (x *ErrorDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_common_v1_common_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ErrorDetail.ProtoReflect.Descriptor instead.
func (*ErrorDetail) Descriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{0}
}

func (x *ErrorDetail) GetErrorCode() int32 {
	if x != nil {
		return x.ErrorCode
	}
	return 0
}

func (x *ErrorDetail) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type BaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AuthToken     string                 `protobuf:"bytes,1,opt,name=auth_token,json=authToken,proto3" json:"auth_token,omitempty"`
	RequestId     string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	ClientVersion string                 `protobuf:"bytes,3,opt,name=client_version,json=clientVersion,proto3" json:"client_version,omitempty"`
	Locale        string                 `protobuf:"bytes,4,opt,name=locale,proto3" json:"locale,omitempty"`
	Timestamp     int64                  `protobuf:"varint,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseRequest) Reset() {
	*x = BaseRequest{}
	mi := &file_api_common_v1_common_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseRequest) ProtoMessage() {}

func (x *BaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_common_v1_common_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseRequest.ProtoReflect.Descriptor instead.
func (*BaseRequest) Descriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{1}
}

func (x *BaseRequest) GetAuthToken() string {
	if x != nil {
		return x.AuthToken
	}
	return ""
}

func (x *BaseRequest) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BaseRequest) GetClientVersion() string {
	if x != nil {
		return x.ClientVersion
	}
	return ""
}

func (x *BaseRequest) GetLocale() string {
	if x != nil {
		return x.Locale
	}
	return ""
}

func (x *BaseRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type BaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        Status                 `protobuf:"varint,1,opt,name=status,proto3,enum=api.common.v1.Status" json:"status,omitempty"`
	RequestId     string                 `protobuf:"bytes,2,opt,name=request_id,json=requestId,proto3" json:"request_id,omitempty"`
	TraceId       string                 `protobuf:"bytes,3,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`
	ErrorDetail   *ErrorDetail           `protobuf:"bytes,4,opt,name=error_detail,json=errorDetail,proto3" json:"error_detail,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BaseResponse) Reset() {
	*x = BaseResponse{}
	mi := &file_api_common_v1_common_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BaseResponse) ProtoMessage() {}

func (x *BaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_common_v1_common_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BaseResponse.ProtoReflect.Descriptor instead.
func (*BaseResponse) Descriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{2}
}

func (x *BaseResponse) GetStatus() Status {
	if x != nil {
		return x.Status
	}
	return Status_STATUS_UNSPECIFIED
}

func (x *BaseResponse) GetRequestId() string {
	if x != nil {
		return x.RequestId
	}
	return ""
}

func (x *BaseResponse) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *BaseResponse) GetErrorDetail() *ErrorDetail {
	if x != nil {
		return x.ErrorDetail
	}
	return nil
}

type CommonResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *BaseResponse          `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CommonResponse) Reset() {
	*x = CommonResponse{}
	mi := &file_api_common_v1_common_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CommonResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CommonResponse) ProtoMessage() {}

func (x *CommonResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_common_v1_common_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CommonResponse.ProtoReflect.Descriptor instead.
func (*CommonResponse) Descriptor() ([]byte, []int) {
	return file_api_common_v1_common_proto_rawDescGZIP(), []int{3}
}

func (x *CommonResponse) GetResponse() *BaseResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

var File_api_common_v1_common_proto protoreflect.FileDescriptor

const file_api_common_v1_common_proto_rawDesc = "" +
	"\n" +
	"\x1aapi/common/v1/common.proto\x12\rapi.common.v1\"F\n" +
	"\vErrorDetail\x12\x1d\n" +
	"\n" +
	"error_code\x18\x01 \x01(\x05R\terrorCode\x12\x18\n" +
	"\amessage\x18\x02 \x01(\tR\amessage\"\xa8\x01\n" +
	"\vBaseRequest\x12\x1d\n" +
	"\n" +
	"auth_token\x18\x01 \x01(\tR\tauthToken\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12%\n" +
	"\x0eclient_version\x18\x03 \x01(\tR\rclientVersion\x12\x16\n" +
	"\x06locale\x18\x04 \x01(\tR\x06locale\x12\x1c\n" +
	"\ttimestamp\x18\x05 \x01(\x03R\ttimestamp\"\xb6\x01\n" +
	"\fBaseResponse\x12-\n" +
	"\x06status\x18\x01 \x01(\x0e2\x15.api.common.v1.StatusR\x06status\x12\x1d\n" +
	"\n" +
	"request_id\x18\x02 \x01(\tR\trequestId\x12\x19\n" +
	"\btrace_id\x18\x03 \x01(\tR\atraceId\x12=\n" +
	"\ferror_detail\x18\x04 \x01(\v2\x1a.api.common.v1.ErrorDetailR\verrorDetail\"I\n" +
	"\x0eCommonResponse\x127\n" +
	"\bresponse\x18\x01 \x01(\v2\x1b.api.common.v1.BaseResponseR\bresponse*\x8c\x02\n" +
	"\n" +
	"DeviceType\x12\x1b\n" +
	"\x17DEVICE_TYPE_UNSPECIFIED\x10\x00\x12\x13\n" +
	"\x0fDEVICE_TYPE_IOS\x10\x01\x12\x17\n" +
	"\x13DEVICE_TYPE_ANDROID\x10\x02\x12\x16\n" +
	"\x12DEVICE_TYPE_MACOSX\x10\x03\x12\x17\n" +
	"\x13DEVICE_TYPE_WINDOWS\x10\x04\x12\x15\n" +
	"\x11DEVICE_TYPE_LINUX\x10\x05\x12\x1a\n" +
	"\x16DEVICE_TYPE_MACOSX_ARM\x10\x06\x12\x1b\n" +
	"\x17DEVICE_TYPE_WINDOWS_ARM\x10\a\x12\x19\n" +
	"\x15DEVICE_TYPE_LINUX_ARM\x10\b\x12\x17\n" +
	"\x13DEVICE_TYPE_HARMONY\x10\t*6\n" +
	"\x05Appid\x12\x15\n" +
	"\x11APPID_UNSPECIFIED\x10\x00\x12\x16\n" +
	"\x12APPID_CALORIE_SCAN\x10\x01*Q\n" +
	"\bChatType\x12\x19\n" +
	"\x15CHAT_TYPE_UNSPECIFIED\x10\x00\x12\x15\n" +
	"\x11CHAT_TYPE_PRIVATE\x10\x01\x12\x13\n" +
	"\x0fCHAT_TYPE_GROUP\x10\x02*\xdc\x01\n" +
	"\x06Status\x12\x16\n" +
	"\x12STATUS_UNSPECIFIED\x10\x00\x12\r\n" +
	"\tSTATUS_OK\x10\x01\x12\x16\n" +
	"\x12STATUS_BAD_REQUEST\x10\x02\x12\x17\n" +
	"\x13STATUS_UNAUTHORIZED\x10\x03\x12\x14\n" +
	"\x10STATUS_FORBIDDEN\x10\x04\x12\x14\n" +
	"\x10STATUS_NOT_FOUND\x10\x05\x12\x13\n" +
	"\x0fSTATUS_CONFLICT\x10\x06\x12\x19\n" +
	"\x15STATUS_INTERNAL_ERROR\x10\a\x12\x1e\n" +
	"\x1aSTATUS_SERVICE_UNAVAILABLE\x10\b*\x8e\x01\n" +
	"\tCommandID\x12\x1a\n" +
	"\x16COMMAND_ID_UNSPECIFIED\x10\x00\x12*\n" +
	"&COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR\x10\x01\x12\x18\n" +
	"\x14COMMAND_ID_HEARTBEAT\x10\x02\x12\x1f\n" +
	"\x1bCOMMAND_ID_TOO_MANY_REQUEST\x10\x03B<Z:code.interestingsoft.com/shared/proto/gen/go/api/common/v1b\x06proto3"

var (
	file_api_common_v1_common_proto_rawDescOnce sync.Once
	file_api_common_v1_common_proto_rawDescData []byte
)

func file_api_common_v1_common_proto_rawDescGZIP() []byte {
	file_api_common_v1_common_proto_rawDescOnce.Do(func() {
		file_api_common_v1_common_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_common_v1_common_proto_rawDesc), len(file_api_common_v1_common_proto_rawDesc)))
	})
	return file_api_common_v1_common_proto_rawDescData
}

var file_api_common_v1_common_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_api_common_v1_common_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_api_common_v1_common_proto_goTypes = []any{
	(DeviceType)(0),        // 0: api.common.v1.DeviceType
	(Appid)(0),             // 1: api.common.v1.Appid
	(ChatType)(0),          // 2: api.common.v1.ChatType
	(Status)(0),            // 3: api.common.v1.Status
	(CommandID)(0),         // 4: api.common.v1.CommandID
	(*ErrorDetail)(nil),    // 5: api.common.v1.ErrorDetail
	(*BaseRequest)(nil),    // 6: api.common.v1.BaseRequest
	(*BaseResponse)(nil),   // 7: api.common.v1.BaseResponse
	(*CommonResponse)(nil), // 8: api.common.v1.CommonResponse
}
var file_api_common_v1_common_proto_depIdxs = []int32{
	3, // 0: api.common.v1.BaseResponse.status:type_name -> api.common.v1.Status
	5, // 1: api.common.v1.BaseResponse.error_detail:type_name -> api.common.v1.ErrorDetail
	7, // 2: api.common.v1.CommonResponse.response:type_name -> api.common.v1.BaseResponse
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_api_common_v1_common_proto_init() }
func file_api_common_v1_common_proto_init() {
	if File_api_common_v1_common_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_common_v1_common_proto_rawDesc), len(file_api_common_v1_common_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_common_v1_common_proto_goTypes,
		DependencyIndexes: file_api_common_v1_common_proto_depIdxs,
		EnumInfos:         file_api_common_v1_common_proto_enumTypes,
		MessageInfos:      file_api_common_v1_common_proto_msgTypes,
	}.Build()
	File_api_common_v1_common_proto = out.File
	file_api_common_v1_common_proto_goTypes = nil
	file_api_common_v1_common_proto_depIdxs = nil
}
