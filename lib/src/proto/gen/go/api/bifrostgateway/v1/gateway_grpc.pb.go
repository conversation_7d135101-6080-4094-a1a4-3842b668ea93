// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             (unknown)
// source: api/bifrostgateway/v1/gateway.proto

package bifrostgateway

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	BifrostGatewayService_PushMessage_FullMethodName = "/api.bifrostgateway.v1.BifrostGatewayService/PushMessage"
)

// BifrostGatewayServiceClient is the client API for BifrostGatewayService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type BifrostGatewayServiceClient interface {
	PushMessage(ctx context.Context, in *PushMessageRequest, opts ...grpc.CallOption) (*PushMessageResponse, error)
}

type bifrostGatewayServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewBifrostGatewayServiceClient(cc grpc.ClientConnInterface) BifrostGatewayServiceClient {
	return &bifrostGatewayServiceClient{cc}
}

func (c *bifrostGatewayServiceClient) PushMessage(ctx context.Context, in *PushMessageRequest, opts ...grpc.CallOption) (*PushMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PushMessageResponse)
	err := c.cc.Invoke(ctx, BifrostGatewayService_PushMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// BifrostGatewayServiceServer is the server API for BifrostGatewayService service.
// All implementations must embed UnimplementedBifrostGatewayServiceServer
// for forward compatibility.
type BifrostGatewayServiceServer interface {
	PushMessage(context.Context, *PushMessageRequest) (*PushMessageResponse, error)
	mustEmbedUnimplementedBifrostGatewayServiceServer()
}

// UnimplementedBifrostGatewayServiceServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedBifrostGatewayServiceServer struct{}

func (UnimplementedBifrostGatewayServiceServer) PushMessage(context.Context, *PushMessageRequest) (*PushMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PushMessage not implemented")
}
func (UnimplementedBifrostGatewayServiceServer) mustEmbedUnimplementedBifrostGatewayServiceServer() {}
func (UnimplementedBifrostGatewayServiceServer) testEmbeddedByValue()                               {}

// UnsafeBifrostGatewayServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to BifrostGatewayServiceServer will
// result in compilation errors.
type UnsafeBifrostGatewayServiceServer interface {
	mustEmbedUnimplementedBifrostGatewayServiceServer()
}

func RegisterBifrostGatewayServiceServer(s grpc.ServiceRegistrar, srv BifrostGatewayServiceServer) {
	// If the following call pancis, it indicates UnimplementedBifrostGatewayServiceServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&BifrostGatewayService_ServiceDesc, srv)
}

func _BifrostGatewayService_PushMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PushMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(BifrostGatewayServiceServer).PushMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: BifrostGatewayService_PushMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(BifrostGatewayServiceServer).PushMessage(ctx, req.(*PushMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// BifrostGatewayService_ServiceDesc is the grpc.ServiceDesc for BifrostGatewayService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var BifrostGatewayService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "api.bifrostgateway.v1.BifrostGatewayService",
	HandlerType: (*BifrostGatewayServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "PushMessage",
			Handler:    _BifrostGatewayService_PushMessage_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/bifrostgateway/v1/gateway.proto",
}
