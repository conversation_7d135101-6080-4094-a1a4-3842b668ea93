// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: api/bifrostgateway/v1/gateway.proto

package bifrostgateway

import (
	v1 "code.interestingsoft.com/shared/proto/gen/go/api/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HandshakeRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PublicKey     []byte                 `protobuf:"bytes,1,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Nonce         []byte                 `protobuf:"bytes,3,opt,name=nonce,proto3" json:"nonce,omitempty"`
	Digest        []byte                 `protobuf:"bytes,4,opt,name=digest,proto3" json:"digest,omitempty"` // sha256(public_key + timestamp + nonce)
	DeviceType    v1.DeviceType          `protobuf:"varint,5,opt,name=device_type,json=deviceType,proto3,enum=api.common.v1.DeviceType" json:"device_type,omitempty"`
	Appid         v1.Appid               `protobuf:"varint,6,opt,name=appid,proto3,enum=api.common.v1.Appid" json:"appid,omitempty"`
	AppVersion    string                 `protobuf:"bytes,7,opt,name=app_version,json=appVersion,proto3" json:"app_version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeRequest) Reset() {
	*x = HandshakeRequest{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeRequest) ProtoMessage() {}

func (x *HandshakeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeRequest.ProtoReflect.Descriptor instead.
func (*HandshakeRequest) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{0}
}

func (x *HandshakeRequest) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

func (x *HandshakeRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *HandshakeRequest) GetNonce() []byte {
	if x != nil {
		return x.Nonce
	}
	return nil
}

func (x *HandshakeRequest) GetDigest() []byte {
	if x != nil {
		return x.Digest
	}
	return nil
}

func (x *HandshakeRequest) GetDeviceType() v1.DeviceType {
	if x != nil {
		return x.DeviceType
	}
	return v1.DeviceType(0)
}

func (x *HandshakeRequest) GetAppid() v1.Appid {
	if x != nil {
		return x.Appid
	}
	return v1.Appid(0)
}

func (x *HandshakeRequest) GetAppVersion() string {
	if x != nil {
		return x.AppVersion
	}
	return ""
}

type HandshakeResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Response      *v1.BaseResponse       `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	PublicKey     []byte                 `protobuf:"bytes,2,opt,name=public_key,json=publicKey,proto3" json:"public_key,omitempty"`
	Timestamp     int64                  `protobuf:"varint,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Nonce         []byte                 `protobuf:"bytes,4,opt,name=nonce,proto3" json:"nonce,omitempty"`
	AuthKeyId     uint64                 `protobuf:"varint,5,opt,name=auth_key_id,json=authKeyId,proto3" json:"auth_key_id,omitempty"`
	Digest        []byte                 `protobuf:"bytes,6,opt,name=digest,proto3" json:"digest,omitempty"` // sha256(public_key + timestamp + nonce)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HandshakeResponse) Reset() {
	*x = HandshakeResponse{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HandshakeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HandshakeResponse) ProtoMessage() {}

func (x *HandshakeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HandshakeResponse.ProtoReflect.Descriptor instead.
func (*HandshakeResponse) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{1}
}

func (x *HandshakeResponse) GetResponse() *v1.BaseResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *HandshakeResponse) GetPublicKey() []byte {
	if x != nil {
		return x.PublicKey
	}
	return nil
}

func (x *HandshakeResponse) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *HandshakeResponse) GetNonce() []byte {
	if x != nil {
		return x.Nonce
	}
	return nil
}

func (x *HandshakeResponse) GetAuthKeyId() uint64 {
	if x != nil {
		return x.AuthKeyId
	}
	return 0
}

func (x *HandshakeResponse) GetDigest() []byte {
	if x != nil {
		return x.Digest
	}
	return nil
}

type TransferPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CommandId     v1.CommandID           `protobuf:"varint,1,opt,name=command_id,json=commandId,proto3,enum=api.common.v1.CommandID" json:"command_id,omitempty"`
	Data          []byte                 `protobuf:"bytes,2,opt,name=data,proto3" json:"data,omitempty"`
	Token         []byte                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TransferPayload) Reset() {
	*x = TransferPayload{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TransferPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TransferPayload) ProtoMessage() {}

func (x *TransferPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TransferPayload.ProtoReflect.Descriptor instead.
func (*TransferPayload) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{2}
}

func (x *TransferPayload) GetCommandId() v1.CommandID {
	if x != nil {
		return x.CommandId
	}
	return v1.CommandID(0)
}

func (x *TransferPayload) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *TransferPayload) GetToken() []byte {
	if x != nil {
		return x.Token
	}
	return nil
}

type HeartbeatRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatRequest) Reset() {
	*x = HeartbeatRequest{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatRequest) ProtoMessage() {}

func (x *HeartbeatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatRequest.ProtoReflect.Descriptor instead.
func (*HeartbeatRequest) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{3}
}

type HeartbeatResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HeartbeatResponse) Reset() {
	*x = HeartbeatResponse{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HeartbeatResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HeartbeatResponse) ProtoMessage() {}

func (x *HeartbeatResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HeartbeatResponse.ProtoReflect.Descriptor instead.
func (*HeartbeatResponse) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{4}
}

type PushMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DeviceIds     []uint64               `protobuf:"varint,1,rep,packed,name=device_ids,json=deviceIds,proto3" json:"device_ids,omitempty"`
	Cmd           int32                  `protobuf:"varint,2,opt,name=cmd,proto3" json:"cmd,omitempty"`
	Payload       []byte                 `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty" mask:"payload"` // @gotags: mask:"payload"
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PushMessageRequest) Reset() {
	*x = PushMessageRequest{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMessageRequest) ProtoMessage() {}

func (x *PushMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMessageRequest.ProtoReflect.Descriptor instead.
func (*PushMessageRequest) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{5}
}

func (x *PushMessageRequest) GetDeviceIds() []uint64 {
	if x != nil {
		return x.DeviceIds
	}
	return nil
}

func (x *PushMessageRequest) GetCmd() int32 {
	if x != nil {
		return x.Cmd
	}
	return 0
}

func (x *PushMessageRequest) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type PushMessageResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Response        *v1.BaseResponse       `protobuf:"bytes,1,opt,name=response,proto3" json:"response,omitempty"`
	FailedDeviceIds []uint64               `protobuf:"varint,2,rep,packed,name=failed_device_ids,json=failedDeviceIds,proto3" json:"failed_device_ids,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *PushMessageResponse) Reset() {
	*x = PushMessageResponse{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PushMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PushMessageResponse) ProtoMessage() {}

func (x *PushMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PushMessageResponse.ProtoReflect.Descriptor instead.
func (*PushMessageResponse) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{6}
}

func (x *PushMessageResponse) GetResponse() *v1.BaseResponse {
	if x != nil {
		return x.Response
	}
	return nil
}

func (x *PushMessageResponse) GetFailedDeviceIds() []uint64 {
	if x != nil {
		return x.FailedDeviceIds
	}
	return nil
}

type RawPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []byte                 `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RawPayload) Reset() {
	*x = RawPayload{}
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawPayload) ProtoMessage() {}

func (x *RawPayload) ProtoReflect() protoreflect.Message {
	mi := &file_api_bifrostgateway_v1_gateway_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawPayload.ProtoReflect.Descriptor instead.
func (*RawPayload) Descriptor() ([]byte, []int) {
	return file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP(), []int{7}
}

func (x *RawPayload) GetData() []byte {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_api_bifrostgateway_v1_gateway_proto protoreflect.FileDescriptor

const file_api_bifrostgateway_v1_gateway_proto_rawDesc = "" +
	"\n" +
	"#api/bifrostgateway/v1/gateway.proto\x12\x15api.bifrostgateway.v1\x1a\x1aapi/common/v1/common.proto\"\x86\x02\n" +
	"\x10HandshakeRequest\x12\x1d\n" +
	"\n" +
	"public_key\x18\x01 \x01(\fR\tpublicKey\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\x12\x14\n" +
	"\x05nonce\x18\x03 \x01(\fR\x05nonce\x12\x16\n" +
	"\x06digest\x18\x04 \x01(\fR\x06digest\x12:\n" +
	"\vdevice_type\x18\x05 \x01(\x0e2\x19.api.common.v1.DeviceTypeR\n" +
	"deviceType\x12*\n" +
	"\x05appid\x18\x06 \x01(\x0e2\x14.api.common.v1.AppidR\x05appid\x12\x1f\n" +
	"\vapp_version\x18\a \x01(\tR\n" +
	"appVersion\"\xd7\x01\n" +
	"\x11HandshakeResponse\x127\n" +
	"\bresponse\x18\x01 \x01(\v2\x1b.api.common.v1.BaseResponseR\bresponse\x12\x1d\n" +
	"\n" +
	"public_key\x18\x02 \x01(\fR\tpublicKey\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\x03R\ttimestamp\x12\x14\n" +
	"\x05nonce\x18\x04 \x01(\fR\x05nonce\x12\x1e\n" +
	"\vauth_key_id\x18\x05 \x01(\x04R\tauthKeyId\x12\x16\n" +
	"\x06digest\x18\x06 \x01(\fR\x06digest\"t\n" +
	"\x0fTransferPayload\x127\n" +
	"\n" +
	"command_id\x18\x01 \x01(\x0e2\x18.api.common.v1.CommandIDR\tcommandId\x12\x12\n" +
	"\x04data\x18\x02 \x01(\fR\x04data\x12\x14\n" +
	"\x05token\x18\x03 \x01(\fR\x05token\"\x12\n" +
	"\x10HeartbeatRequest\"\x13\n" +
	"\x11HeartbeatResponse\"_\n" +
	"\x12PushMessageRequest\x12\x1d\n" +
	"\n" +
	"device_ids\x18\x01 \x03(\x04R\tdeviceIds\x12\x10\n" +
	"\x03cmd\x18\x02 \x01(\x05R\x03cmd\x12\x18\n" +
	"\apayload\x18\x03 \x01(\fR\apayload\"z\n" +
	"\x13PushMessageResponse\x127\n" +
	"\bresponse\x18\x01 \x01(\v2\x1b.api.common.v1.BaseResponseR\bresponse\x12*\n" +
	"\x11failed_device_ids\x18\x02 \x03(\x04R\x0ffailedDeviceIds\" \n" +
	"\n" +
	"RawPayload\x12\x12\n" +
	"\x04data\x18\x01 \x01(\fR\x04data2}\n" +
	"\x15BifrostGatewayService\x12d\n" +
	"\vPushMessage\x12).api.bifrostgateway.v1.PushMessageRequest\x1a*.api.bifrostgateway.v1.PushMessageResponseBAZ?code.interestingsoft.com/shared/proto/gen/go/api/bifrostgatewayb\x06proto3"

var (
	file_api_bifrostgateway_v1_gateway_proto_rawDescOnce sync.Once
	file_api_bifrostgateway_v1_gateway_proto_rawDescData []byte
)

func file_api_bifrostgateway_v1_gateway_proto_rawDescGZIP() []byte {
	file_api_bifrostgateway_v1_gateway_proto_rawDescOnce.Do(func() {
		file_api_bifrostgateway_v1_gateway_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_api_bifrostgateway_v1_gateway_proto_rawDesc), len(file_api_bifrostgateway_v1_gateway_proto_rawDesc)))
	})
	return file_api_bifrostgateway_v1_gateway_proto_rawDescData
}

var file_api_bifrostgateway_v1_gateway_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_api_bifrostgateway_v1_gateway_proto_goTypes = []any{
	(*HandshakeRequest)(nil),    // 0: api.bifrostgateway.v1.HandshakeRequest
	(*HandshakeResponse)(nil),   // 1: api.bifrostgateway.v1.HandshakeResponse
	(*TransferPayload)(nil),     // 2: api.bifrostgateway.v1.TransferPayload
	(*HeartbeatRequest)(nil),    // 3: api.bifrostgateway.v1.HeartbeatRequest
	(*HeartbeatResponse)(nil),   // 4: api.bifrostgateway.v1.HeartbeatResponse
	(*PushMessageRequest)(nil),  // 5: api.bifrostgateway.v1.PushMessageRequest
	(*PushMessageResponse)(nil), // 6: api.bifrostgateway.v1.PushMessageResponse
	(*RawPayload)(nil),          // 7: api.bifrostgateway.v1.RawPayload
	(v1.DeviceType)(0),          // 8: api.common.v1.DeviceType
	(v1.Appid)(0),               // 9: api.common.v1.Appid
	(*v1.BaseResponse)(nil),     // 10: api.common.v1.BaseResponse
	(v1.CommandID)(0),           // 11: api.common.v1.CommandID
}
var file_api_bifrostgateway_v1_gateway_proto_depIdxs = []int32{
	8,  // 0: api.bifrostgateway.v1.HandshakeRequest.device_type:type_name -> api.common.v1.DeviceType
	9,  // 1: api.bifrostgateway.v1.HandshakeRequest.appid:type_name -> api.common.v1.Appid
	10, // 2: api.bifrostgateway.v1.HandshakeResponse.response:type_name -> api.common.v1.BaseResponse
	11, // 3: api.bifrostgateway.v1.TransferPayload.command_id:type_name -> api.common.v1.CommandID
	10, // 4: api.bifrostgateway.v1.PushMessageResponse.response:type_name -> api.common.v1.BaseResponse
	5,  // 5: api.bifrostgateway.v1.BifrostGatewayService.PushMessage:input_type -> api.bifrostgateway.v1.PushMessageRequest
	6,  // 6: api.bifrostgateway.v1.BifrostGatewayService.PushMessage:output_type -> api.bifrostgateway.v1.PushMessageResponse
	6,  // [6:7] is the sub-list for method output_type
	5,  // [5:6] is the sub-list for method input_type
	5,  // [5:5] is the sub-list for extension type_name
	5,  // [5:5] is the sub-list for extension extendee
	0,  // [0:5] is the sub-list for field type_name
}

func init() { file_api_bifrostgateway_v1_gateway_proto_init() }
func file_api_bifrostgateway_v1_gateway_proto_init() {
	if File_api_bifrostgateway_v1_gateway_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_api_bifrostgateway_v1_gateway_proto_rawDesc), len(file_api_bifrostgateway_v1_gateway_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_bifrostgateway_v1_gateway_proto_goTypes,
		DependencyIndexes: file_api_bifrostgateway_v1_gateway_proto_depIdxs,
		MessageInfos:      file_api_bifrostgateway_v1_gateway_proto_msgTypes,
	}.Build()
	File_api_bifrostgateway_v1_gateway_proto = out.File
	file_api_bifrostgateway_v1_gateway_proto_goTypes = nil
	file_api_bifrostgateway_v1_gateway_proto_depIdxs = nil
}
