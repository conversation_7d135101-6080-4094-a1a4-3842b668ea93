// This is a generated file - do not edit.
//
// Generated from api/bifrostgateway/v1/gateway.proto.

// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names
// ignore_for_file: curly_braces_in_flow_control_structures
// ignore_for_file: deprecated_member_use_from_same_package, library_prefixes
// ignore_for_file: non_constant_identifier_names

import 'dart:async' as $async;
import 'dart:core' as $core;

import 'package:fixnum/fixnum.dart' as $fixnum;
import 'package:protobuf/protobuf.dart' as $pb;

import '../../common/v1/common.pb.dart' as $0;

export 'package:protobuf/protobuf.dart' show GeneratedMessageGenericExtensions;

class HandshakeRequest extends $pb.GeneratedMessage {
  factory HandshakeRequest({
    $core.List<$core.int>? publicKey,
    $fixnum.Int64? timestamp,
    $core.List<$core.int>? nonce,
    $core.List<$core.int>? digest,
    $0.DeviceType? deviceType,
    $0.Appid? appid,
    $core.String? appVersion,
  }) {
    final result = create();
    if (publicKey != null) result.publicKey = publicKey;
    if (timestamp != null) result.timestamp = timestamp;
    if (nonce != null) result.nonce = nonce;
    if (digest != null) result.digest = digest;
    if (deviceType != null) result.deviceType = deviceType;
    if (appid != null) result.appid = appid;
    if (appVersion != null) result.appVersion = appVersion;
    return result;
  }

  HandshakeRequest._();

  factory HandshakeRequest.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory HandshakeRequest.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'HandshakeRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'publicKey', $pb.PbFieldType.OY)
    ..aInt64(2, _omitFieldNames ? '' : 'timestamp')
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'nonce', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(4, _omitFieldNames ? '' : 'digest', $pb.PbFieldType.OY)
    ..e<$0.DeviceType>(5, _omitFieldNames ? '' : 'deviceType', $pb.PbFieldType.OE, defaultOrMaker: $0.DeviceType.DEVICE_TYPE_UNSPECIFIED, valueOf: $0.DeviceType.valueOf, enumValues: $0.DeviceType.values)
    ..e<$0.Appid>(6, _omitFieldNames ? '' : 'appid', $pb.PbFieldType.OE, defaultOrMaker: $0.Appid.APPID_UNSPECIFIED, valueOf: $0.Appid.valueOf, enumValues: $0.Appid.values)
    ..aOS(7, _omitFieldNames ? '' : 'appVersion')
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HandshakeRequest clone() => HandshakeRequest()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HandshakeRequest copyWith(void Function(HandshakeRequest) updates) => super.copyWith((message) => updates(message as HandshakeRequest)) as HandshakeRequest;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static HandshakeRequest create() => HandshakeRequest._();
  @$core.override
  HandshakeRequest createEmptyInstance() => create();
  static $pb.PbList<HandshakeRequest> createRepeated() => $pb.PbList<HandshakeRequest>();
  @$core.pragma('dart2js:noInline')
  static HandshakeRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HandshakeRequest>(create);
  static HandshakeRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get publicKey => $_getN(0);
  @$pb.TagNumber(1)
  set publicKey($core.List<$core.int> value) => $_setBytes(0, value);
  @$pb.TagNumber(1)
  $core.bool hasPublicKey() => $_has(0);
  @$pb.TagNumber(1)
  void clearPublicKey() => $_clearField(1);

  @$pb.TagNumber(2)
  $fixnum.Int64 get timestamp => $_getI64(1);
  @$pb.TagNumber(2)
  set timestamp($fixnum.Int64 value) => $_setInt64(1, value);
  @$pb.TagNumber(2)
  $core.bool hasTimestamp() => $_has(1);
  @$pb.TagNumber(2)
  void clearTimestamp() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get nonce => $_getN(2);
  @$pb.TagNumber(3)
  set nonce($core.List<$core.int> value) => $_setBytes(2, value);
  @$pb.TagNumber(3)
  $core.bool hasNonce() => $_has(2);
  @$pb.TagNumber(3)
  void clearNonce() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.int> get digest => $_getN(3);
  @$pb.TagNumber(4)
  set digest($core.List<$core.int> value) => $_setBytes(3, value);
  @$pb.TagNumber(4)
  $core.bool hasDigest() => $_has(3);
  @$pb.TagNumber(4)
  void clearDigest() => $_clearField(4);

  @$pb.TagNumber(5)
  $0.DeviceType get deviceType => $_getN(4);
  @$pb.TagNumber(5)
  set deviceType($0.DeviceType value) => $_setField(5, value);
  @$pb.TagNumber(5)
  $core.bool hasDeviceType() => $_has(4);
  @$pb.TagNumber(5)
  void clearDeviceType() => $_clearField(5);

  @$pb.TagNumber(6)
  $0.Appid get appid => $_getN(5);
  @$pb.TagNumber(6)
  set appid($0.Appid value) => $_setField(6, value);
  @$pb.TagNumber(6)
  $core.bool hasAppid() => $_has(5);
  @$pb.TagNumber(6)
  void clearAppid() => $_clearField(6);

  @$pb.TagNumber(7)
  $core.String get appVersion => $_getSZ(6);
  @$pb.TagNumber(7)
  set appVersion($core.String value) => $_setString(6, value);
  @$pb.TagNumber(7)
  $core.bool hasAppVersion() => $_has(6);
  @$pb.TagNumber(7)
  void clearAppVersion() => $_clearField(7);
}

class HandshakeResponse extends $pb.GeneratedMessage {
  factory HandshakeResponse({
    $0.BaseResponse? response,
    $core.List<$core.int>? publicKey,
    $fixnum.Int64? timestamp,
    $core.List<$core.int>? nonce,
    $fixnum.Int64? authKeyId,
    $core.List<$core.int>? digest,
  }) {
    final result = create();
    if (response != null) result.response = response;
    if (publicKey != null) result.publicKey = publicKey;
    if (timestamp != null) result.timestamp = timestamp;
    if (nonce != null) result.nonce = nonce;
    if (authKeyId != null) result.authKeyId = authKeyId;
    if (digest != null) result.digest = digest;
    return result;
  }

  HandshakeResponse._();

  factory HandshakeResponse.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory HandshakeResponse.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'HandshakeResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..aOM<$0.BaseResponse>(1, _omitFieldNames ? '' : 'response', subBuilder: $0.BaseResponse.create)
    ..a<$core.List<$core.int>>(2, _omitFieldNames ? '' : 'publicKey', $pb.PbFieldType.OY)
    ..aInt64(3, _omitFieldNames ? '' : 'timestamp')
    ..a<$core.List<$core.int>>(4, _omitFieldNames ? '' : 'nonce', $pb.PbFieldType.OY)
    ..a<$fixnum.Int64>(5, _omitFieldNames ? '' : 'authKeyId', $pb.PbFieldType.OU6, defaultOrMaker: $fixnum.Int64.ZERO)
    ..a<$core.List<$core.int>>(6, _omitFieldNames ? '' : 'digest', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HandshakeResponse clone() => HandshakeResponse()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HandshakeResponse copyWith(void Function(HandshakeResponse) updates) => super.copyWith((message) => updates(message as HandshakeResponse)) as HandshakeResponse;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static HandshakeResponse create() => HandshakeResponse._();
  @$core.override
  HandshakeResponse createEmptyInstance() => create();
  static $pb.PbList<HandshakeResponse> createRepeated() => $pb.PbList<HandshakeResponse>();
  @$core.pragma('dart2js:noInline')
  static HandshakeResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HandshakeResponse>(create);
  static HandshakeResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $0.BaseResponse get response => $_getN(0);
  @$pb.TagNumber(1)
  set response($0.BaseResponse value) => $_setField(1, value);
  @$pb.TagNumber(1)
  $core.bool hasResponse() => $_has(0);
  @$pb.TagNumber(1)
  void clearResponse() => $_clearField(1);
  @$pb.TagNumber(1)
  $0.BaseResponse ensureResponse() => $_ensure(0);

  @$pb.TagNumber(2)
  $core.List<$core.int> get publicKey => $_getN(1);
  @$pb.TagNumber(2)
  set publicKey($core.List<$core.int> value) => $_setBytes(1, value);
  @$pb.TagNumber(2)
  $core.bool hasPublicKey() => $_has(1);
  @$pb.TagNumber(2)
  void clearPublicKey() => $_clearField(2);

  @$pb.TagNumber(3)
  $fixnum.Int64 get timestamp => $_getI64(2);
  @$pb.TagNumber(3)
  set timestamp($fixnum.Int64 value) => $_setInt64(2, value);
  @$pb.TagNumber(3)
  $core.bool hasTimestamp() => $_has(2);
  @$pb.TagNumber(3)
  void clearTimestamp() => $_clearField(3);

  @$pb.TagNumber(4)
  $core.List<$core.int> get nonce => $_getN(3);
  @$pb.TagNumber(4)
  set nonce($core.List<$core.int> value) => $_setBytes(3, value);
  @$pb.TagNumber(4)
  $core.bool hasNonce() => $_has(3);
  @$pb.TagNumber(4)
  void clearNonce() => $_clearField(4);

  @$pb.TagNumber(5)
  $fixnum.Int64 get authKeyId => $_getI64(4);
  @$pb.TagNumber(5)
  set authKeyId($fixnum.Int64 value) => $_setInt64(4, value);
  @$pb.TagNumber(5)
  $core.bool hasAuthKeyId() => $_has(4);
  @$pb.TagNumber(5)
  void clearAuthKeyId() => $_clearField(5);

  @$pb.TagNumber(6)
  $core.List<$core.int> get digest => $_getN(5);
  @$pb.TagNumber(6)
  set digest($core.List<$core.int> value) => $_setBytes(5, value);
  @$pb.TagNumber(6)
  $core.bool hasDigest() => $_has(5);
  @$pb.TagNumber(6)
  void clearDigest() => $_clearField(6);
}

class TransferPayload extends $pb.GeneratedMessage {
  factory TransferPayload({
    $0.CommandID? commandId,
    $core.List<$core.int>? data,
    $core.List<$core.int>? token,
  }) {
    final result = create();
    if (commandId != null) result.commandId = commandId;
    if (data != null) result.data = data;
    if (token != null) result.token = token;
    return result;
  }

  TransferPayload._();

  factory TransferPayload.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory TransferPayload.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'TransferPayload', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..e<$0.CommandID>(1, _omitFieldNames ? '' : 'commandId', $pb.PbFieldType.OE, defaultOrMaker: $0.CommandID.COMMAND_ID_UNSPECIFIED, valueOf: $0.CommandID.valueOf, enumValues: $0.CommandID.values)
    ..a<$core.List<$core.int>>(2, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'token', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  TransferPayload clone() => TransferPayload()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  TransferPayload copyWith(void Function(TransferPayload) updates) => super.copyWith((message) => updates(message as TransferPayload)) as TransferPayload;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static TransferPayload create() => TransferPayload._();
  @$core.override
  TransferPayload createEmptyInstance() => create();
  static $pb.PbList<TransferPayload> createRepeated() => $pb.PbList<TransferPayload>();
  @$core.pragma('dart2js:noInline')
  static TransferPayload getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<TransferPayload>(create);
  static TransferPayload? _defaultInstance;

  @$pb.TagNumber(1)
  $0.CommandID get commandId => $_getN(0);
  @$pb.TagNumber(1)
  set commandId($0.CommandID value) => $_setField(1, value);
  @$pb.TagNumber(1)
  $core.bool hasCommandId() => $_has(0);
  @$pb.TagNumber(1)
  void clearCommandId() => $_clearField(1);

  @$pb.TagNumber(2)
  $core.List<$core.int> get data => $_getN(1);
  @$pb.TagNumber(2)
  set data($core.List<$core.int> value) => $_setBytes(1, value);
  @$pb.TagNumber(2)
  $core.bool hasData() => $_has(1);
  @$pb.TagNumber(2)
  void clearData() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get token => $_getN(2);
  @$pb.TagNumber(3)
  set token($core.List<$core.int> value) => $_setBytes(2, value);
  @$pb.TagNumber(3)
  $core.bool hasToken() => $_has(2);
  @$pb.TagNumber(3)
  void clearToken() => $_clearField(3);
}

class HeartbeatRequest extends $pb.GeneratedMessage {
  factory HeartbeatRequest() => create();

  HeartbeatRequest._();

  factory HeartbeatRequest.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory HeartbeatRequest.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'HeartbeatRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HeartbeatRequest clone() => HeartbeatRequest()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HeartbeatRequest copyWith(void Function(HeartbeatRequest) updates) => super.copyWith((message) => updates(message as HeartbeatRequest)) as HeartbeatRequest;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static HeartbeatRequest create() => HeartbeatRequest._();
  @$core.override
  HeartbeatRequest createEmptyInstance() => create();
  static $pb.PbList<HeartbeatRequest> createRepeated() => $pb.PbList<HeartbeatRequest>();
  @$core.pragma('dart2js:noInline')
  static HeartbeatRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HeartbeatRequest>(create);
  static HeartbeatRequest? _defaultInstance;
}

class HeartbeatResponse extends $pb.GeneratedMessage {
  factory HeartbeatResponse() => create();

  HeartbeatResponse._();

  factory HeartbeatResponse.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory HeartbeatResponse.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'HeartbeatResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HeartbeatResponse clone() => HeartbeatResponse()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  HeartbeatResponse copyWith(void Function(HeartbeatResponse) updates) => super.copyWith((message) => updates(message as HeartbeatResponse)) as HeartbeatResponse;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static HeartbeatResponse create() => HeartbeatResponse._();
  @$core.override
  HeartbeatResponse createEmptyInstance() => create();
  static $pb.PbList<HeartbeatResponse> createRepeated() => $pb.PbList<HeartbeatResponse>();
  @$core.pragma('dart2js:noInline')
  static HeartbeatResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<HeartbeatResponse>(create);
  static HeartbeatResponse? _defaultInstance;
}

class PushMessageRequest extends $pb.GeneratedMessage {
  factory PushMessageRequest({
    $core.Iterable<$fixnum.Int64>? deviceIds,
    $core.int? cmd,
    $core.List<$core.int>? payload,
  }) {
    final result = create();
    if (deviceIds != null) result.deviceIds.addAll(deviceIds);
    if (cmd != null) result.cmd = cmd;
    if (payload != null) result.payload = payload;
    return result;
  }

  PushMessageRequest._();

  factory PushMessageRequest.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory PushMessageRequest.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PushMessageRequest', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..p<$fixnum.Int64>(1, _omitFieldNames ? '' : 'deviceIds', $pb.PbFieldType.KU6)
    ..a<$core.int>(2, _omitFieldNames ? '' : 'cmd', $pb.PbFieldType.O3)
    ..a<$core.List<$core.int>>(3, _omitFieldNames ? '' : 'payload', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  PushMessageRequest clone() => PushMessageRequest()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  PushMessageRequest copyWith(void Function(PushMessageRequest) updates) => super.copyWith((message) => updates(message as PushMessageRequest)) as PushMessageRequest;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PushMessageRequest create() => PushMessageRequest._();
  @$core.override
  PushMessageRequest createEmptyInstance() => create();
  static $pb.PbList<PushMessageRequest> createRepeated() => $pb.PbList<PushMessageRequest>();
  @$core.pragma('dart2js:noInline')
  static PushMessageRequest getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PushMessageRequest>(create);
  static PushMessageRequest? _defaultInstance;

  @$pb.TagNumber(1)
  $pb.PbList<$fixnum.Int64> get deviceIds => $_getList(0);

  @$pb.TagNumber(2)
  $core.int get cmd => $_getIZ(1);
  @$pb.TagNumber(2)
  set cmd($core.int value) => $_setSignedInt32(1, value);
  @$pb.TagNumber(2)
  $core.bool hasCmd() => $_has(1);
  @$pb.TagNumber(2)
  void clearCmd() => $_clearField(2);

  @$pb.TagNumber(3)
  $core.List<$core.int> get payload => $_getN(2);
  @$pb.TagNumber(3)
  set payload($core.List<$core.int> value) => $_setBytes(2, value);
  @$pb.TagNumber(3)
  $core.bool hasPayload() => $_has(2);
  @$pb.TagNumber(3)
  void clearPayload() => $_clearField(3);
}

class PushMessageResponse extends $pb.GeneratedMessage {
  factory PushMessageResponse({
    $0.BaseResponse? response,
    $core.Iterable<$fixnum.Int64>? failedDeviceIds,
  }) {
    final result = create();
    if (response != null) result.response = response;
    if (failedDeviceIds != null) result.failedDeviceIds.addAll(failedDeviceIds);
    return result;
  }

  PushMessageResponse._();

  factory PushMessageResponse.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory PushMessageResponse.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'PushMessageResponse', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..aOM<$0.BaseResponse>(1, _omitFieldNames ? '' : 'response', subBuilder: $0.BaseResponse.create)
    ..p<$fixnum.Int64>(2, _omitFieldNames ? '' : 'failedDeviceIds', $pb.PbFieldType.KU6)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  PushMessageResponse clone() => PushMessageResponse()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  PushMessageResponse copyWith(void Function(PushMessageResponse) updates) => super.copyWith((message) => updates(message as PushMessageResponse)) as PushMessageResponse;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static PushMessageResponse create() => PushMessageResponse._();
  @$core.override
  PushMessageResponse createEmptyInstance() => create();
  static $pb.PbList<PushMessageResponse> createRepeated() => $pb.PbList<PushMessageResponse>();
  @$core.pragma('dart2js:noInline')
  static PushMessageResponse getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<PushMessageResponse>(create);
  static PushMessageResponse? _defaultInstance;

  @$pb.TagNumber(1)
  $0.BaseResponse get response => $_getN(0);
  @$pb.TagNumber(1)
  set response($0.BaseResponse value) => $_setField(1, value);
  @$pb.TagNumber(1)
  $core.bool hasResponse() => $_has(0);
  @$pb.TagNumber(1)
  void clearResponse() => $_clearField(1);
  @$pb.TagNumber(1)
  $0.BaseResponse ensureResponse() => $_ensure(0);

  @$pb.TagNumber(2)
  $pb.PbList<$fixnum.Int64> get failedDeviceIds => $_getList(1);
}

class RawPayload extends $pb.GeneratedMessage {
  factory RawPayload({
    $core.List<$core.int>? data,
  }) {
    final result = create();
    if (data != null) result.data = data;
    return result;
  }

  RawPayload._();

  factory RawPayload.fromBuffer($core.List<$core.int> data, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromBuffer(data, registry);
  factory RawPayload.fromJson($core.String json, [$pb.ExtensionRegistry registry = $pb.ExtensionRegistry.EMPTY]) => create()..mergeFromJson(json, registry);

  static final $pb.BuilderInfo _i = $pb.BuilderInfo(_omitMessageNames ? '' : 'RawPayload', package: const $pb.PackageName(_omitMessageNames ? '' : 'api.bifrostgateway.v1'), createEmptyInstance: create)
    ..a<$core.List<$core.int>>(1, _omitFieldNames ? '' : 'data', $pb.PbFieldType.OY)
    ..hasRequiredFields = false
  ;

  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  RawPayload clone() => RawPayload()..mergeFromMessage(this);
  @$core.Deprecated('See https://github.com/google/protobuf.dart/issues/998.')
  RawPayload copyWith(void Function(RawPayload) updates) => super.copyWith((message) => updates(message as RawPayload)) as RawPayload;

  @$core.override
  $pb.BuilderInfo get info_ => _i;

  @$core.pragma('dart2js:noInline')
  static RawPayload create() => RawPayload._();
  @$core.override
  RawPayload createEmptyInstance() => create();
  static $pb.PbList<RawPayload> createRepeated() => $pb.PbList<RawPayload>();
  @$core.pragma('dart2js:noInline')
  static RawPayload getDefault() => _defaultInstance ??= $pb.GeneratedMessage.$_defaultFor<RawPayload>(create);
  static RawPayload? _defaultInstance;

  @$pb.TagNumber(1)
  $core.List<$core.int> get data => $_getN(0);
  @$pb.TagNumber(1)
  set data($core.List<$core.int> value) => $_setBytes(0, value);
  @$pb.TagNumber(1)
  $core.bool hasData() => $_has(0);
  @$pb.TagNumber(1)
  void clearData() => $_clearField(1);
}

class BifrostGatewayServiceApi {
  final $pb.RpcClient _client;

  BifrostGatewayServiceApi(this._client);

  $async.Future<PushMessageResponse> pushMessage($pb.ClientContext? ctx, PushMessageRequest request) =>
    _client.invoke<PushMessageResponse>(ctx, 'BifrostGatewayService', 'PushMessage', request, PushMessageResponse())
  ;
}


const $core.bool _omitFieldNames = $core.bool.fromEnvironment('protobuf.omit_field_names');
const $core.bool _omitMessageNames = $core.bool.fromEnvironment('protobuf.omit_message_names');
