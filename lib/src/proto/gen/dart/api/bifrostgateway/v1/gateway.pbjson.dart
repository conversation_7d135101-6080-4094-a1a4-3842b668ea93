// This is a generated file - do not edit.
//
// Generated from api/bifrostgateway/v1/gateway.proto.

// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names
// ignore_for_file: curly_braces_in_flow_control_structures
// ignore_for_file: deprecated_member_use_from_same_package, library_prefixes
// ignore_for_file: non_constant_identifier_names, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

import '../../common/v1/common.pbjson.dart' as $0;

@$core.Deprecated('Use handshakeRequestDescriptor instead')
const HandshakeRequest$json = {
  '1': 'HandshakeRequest',
  '2': [
    {'1': 'public_key', '3': 1, '4': 1, '5': 12, '10': 'publicKey'},
    {'1': 'timestamp', '3': 2, '4': 1, '5': 3, '10': 'timestamp'},
    {'1': 'nonce', '3': 3, '4': 1, '5': 12, '10': 'nonce'},
    {'1': 'digest', '3': 4, '4': 1, '5': 12, '10': 'digest'},
    {'1': 'device_type', '3': 5, '4': 1, '5': 14, '6': '.api.common.v1.DeviceType', '10': 'deviceType'},
    {'1': 'appid', '3': 6, '4': 1, '5': 14, '6': '.api.common.v1.Appid', '10': 'appid'},
    {'1': 'app_version', '3': 7, '4': 1, '5': 9, '10': 'appVersion'},
  ],
};

/// Descriptor for `HandshakeRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List handshakeRequestDescriptor = $convert.base64Decode(
    'ChBIYW5kc2hha2VSZXF1ZXN0Eh0KCnB1YmxpY19rZXkYASABKAxSCXB1YmxpY0tleRIcCgl0aW'
    '1lc3RhbXAYAiABKANSCXRpbWVzdGFtcBIUCgVub25jZRgDIAEoDFIFbm9uY2USFgoGZGlnZXN0'
    'GAQgASgMUgZkaWdlc3QSOgoLZGV2aWNlX3R5cGUYBSABKA4yGS5hcGkuY29tbW9uLnYxLkRldm'
    'ljZVR5cGVSCmRldmljZVR5cGUSKgoFYXBwaWQYBiABKA4yFC5hcGkuY29tbW9uLnYxLkFwcGlk'
    'UgVhcHBpZBIfCgthcHBfdmVyc2lvbhgHIAEoCVIKYXBwVmVyc2lvbg==');

@$core.Deprecated('Use handshakeResponseDescriptor instead')
const HandshakeResponse$json = {
  '1': 'HandshakeResponse',
  '2': [
    {'1': 'response', '3': 1, '4': 1, '5': 11, '6': '.api.common.v1.BaseResponse', '10': 'response'},
    {'1': 'public_key', '3': 2, '4': 1, '5': 12, '10': 'publicKey'},
    {'1': 'timestamp', '3': 3, '4': 1, '5': 3, '10': 'timestamp'},
    {'1': 'nonce', '3': 4, '4': 1, '5': 12, '10': 'nonce'},
    {'1': 'auth_key_id', '3': 5, '4': 1, '5': 4, '10': 'authKeyId'},
    {'1': 'digest', '3': 6, '4': 1, '5': 12, '10': 'digest'},
  ],
};

/// Descriptor for `HandshakeResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List handshakeResponseDescriptor = $convert.base64Decode(
    'ChFIYW5kc2hha2VSZXNwb25zZRI3CghyZXNwb25zZRgBIAEoCzIbLmFwaS5jb21tb24udjEuQm'
    'FzZVJlc3BvbnNlUghyZXNwb25zZRIdCgpwdWJsaWNfa2V5GAIgASgMUglwdWJsaWNLZXkSHAoJ'
    'dGltZXN0YW1wGAMgASgDUgl0aW1lc3RhbXASFAoFbm9uY2UYBCABKAxSBW5vbmNlEh4KC2F1dG'
    'hfa2V5X2lkGAUgASgEUglhdXRoS2V5SWQSFgoGZGlnZXN0GAYgASgMUgZkaWdlc3Q=');

@$core.Deprecated('Use transferPayloadDescriptor instead')
const TransferPayload$json = {
  '1': 'TransferPayload',
  '2': [
    {'1': 'command_id', '3': 1, '4': 1, '5': 14, '6': '.api.common.v1.CommandID', '10': 'commandId'},
    {'1': 'data', '3': 2, '4': 1, '5': 12, '10': 'data'},
    {'1': 'token', '3': 3, '4': 1, '5': 12, '10': 'token'},
  ],
};

/// Descriptor for `TransferPayload`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List transferPayloadDescriptor = $convert.base64Decode(
    'Cg9UcmFuc2ZlclBheWxvYWQSNwoKY29tbWFuZF9pZBgBIAEoDjIYLmFwaS5jb21tb24udjEuQ2'
    '9tbWFuZElEUgljb21tYW5kSWQSEgoEZGF0YRgCIAEoDFIEZGF0YRIUCgV0b2tlbhgDIAEoDFIF'
    'dG9rZW4=');

@$core.Deprecated('Use heartbeatRequestDescriptor instead')
const HeartbeatRequest$json = {
  '1': 'HeartbeatRequest',
};

/// Descriptor for `HeartbeatRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List heartbeatRequestDescriptor = $convert.base64Decode(
    'ChBIZWFydGJlYXRSZXF1ZXN0');

@$core.Deprecated('Use heartbeatResponseDescriptor instead')
const HeartbeatResponse$json = {
  '1': 'HeartbeatResponse',
};

/// Descriptor for `HeartbeatResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List heartbeatResponseDescriptor = $convert.base64Decode(
    'ChFIZWFydGJlYXRSZXNwb25zZQ==');

@$core.Deprecated('Use pushMessageRequestDescriptor instead')
const PushMessageRequest$json = {
  '1': 'PushMessageRequest',
  '2': [
    {'1': 'device_ids', '3': 1, '4': 3, '5': 4, '10': 'deviceIds'},
    {'1': 'cmd', '3': 2, '4': 1, '5': 5, '10': 'cmd'},
    {'1': 'payload', '3': 3, '4': 1, '5': 12, '10': 'payload'},
  ],
};

/// Descriptor for `PushMessageRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pushMessageRequestDescriptor = $convert.base64Decode(
    'ChJQdXNoTWVzc2FnZVJlcXVlc3QSHQoKZGV2aWNlX2lkcxgBIAMoBFIJZGV2aWNlSWRzEhAKA2'
    'NtZBgCIAEoBVIDY21kEhgKB3BheWxvYWQYAyABKAxSB3BheWxvYWQ=');

@$core.Deprecated('Use pushMessageResponseDescriptor instead')
const PushMessageResponse$json = {
  '1': 'PushMessageResponse',
  '2': [
    {'1': 'response', '3': 1, '4': 1, '5': 11, '6': '.api.common.v1.BaseResponse', '10': 'response'},
    {'1': 'failed_device_ids', '3': 2, '4': 3, '5': 4, '10': 'failedDeviceIds'},
  ],
};

/// Descriptor for `PushMessageResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List pushMessageResponseDescriptor = $convert.base64Decode(
    'ChNQdXNoTWVzc2FnZVJlc3BvbnNlEjcKCHJlc3BvbnNlGAEgASgLMhsuYXBpLmNvbW1vbi52MS'
    '5CYXNlUmVzcG9uc2VSCHJlc3BvbnNlEioKEWZhaWxlZF9kZXZpY2VfaWRzGAIgAygEUg9mYWls'
    'ZWREZXZpY2VJZHM=');

@$core.Deprecated('Use rawPayloadDescriptor instead')
const RawPayload$json = {
  '1': 'RawPayload',
  '2': [
    {'1': 'data', '3': 1, '4': 1, '5': 12, '10': 'data'},
  ],
};

/// Descriptor for `RawPayload`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List rawPayloadDescriptor = $convert.base64Decode(
    'CgpSYXdQYXlsb2FkEhIKBGRhdGEYASABKAxSBGRhdGE=');

const $core.Map<$core.String, $core.dynamic> BifrostGatewayServiceBase$json = {
  '1': 'BifrostGatewayService',
  '2': [
    {'1': 'PushMessage', '2': '.api.bifrostgateway.v1.PushMessageRequest', '3': '.api.bifrostgateway.v1.PushMessageResponse'},
  ],
};

@$core.Deprecated('Use bifrostGatewayServiceDescriptor instead')
const $core.Map<$core.String, $core.Map<$core.String, $core.dynamic>> BifrostGatewayServiceBase$messageJson = {
  '.api.bifrostgateway.v1.PushMessageRequest': PushMessageRequest$json,
  '.api.bifrostgateway.v1.PushMessageResponse': PushMessageResponse$json,
  '.api.common.v1.BaseResponse': $0.BaseResponse$json,
  '.api.common.v1.ErrorDetail': $0.ErrorDetail$json,
};

/// Descriptor for `BifrostGatewayService`. Decode as a `google.protobuf.ServiceDescriptorProto`.
final $typed_data.Uint8List bifrostGatewayServiceDescriptor = $convert.base64Decode(
    'ChVCaWZyb3N0R2F0ZXdheVNlcnZpY2USZAoLUHVzaE1lc3NhZ2USKS5hcGkuYmlmcm9zdGdhdG'
    'V3YXkudjEuUHVzaE1lc3NhZ2VSZXF1ZXN0GiouYXBpLmJpZnJvc3RnYXRld2F5LnYxLlB1c2hN'
    'ZXNzYWdlUmVzcG9uc2U=');

