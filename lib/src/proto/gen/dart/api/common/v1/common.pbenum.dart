// This is a generated file - do not edit.
//
// Generated from api/common/v1/common.proto.

// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names
// ignore_for_file: curly_braces_in_flow_control_structures
// ignore_for_file: deprecated_member_use_from_same_package, library_prefixes
// ignore_for_file: non_constant_identifier_names

import 'dart:core' as $core;

import 'package:protobuf/protobuf.dart' as $pb;

class DeviceType extends $pb.ProtobufEnum {
  static const DeviceType DEVICE_TYPE_UNSPECIFIED = DeviceType._(0, _omitEnumNames ? '' : 'DEVICE_TYPE_UNSPECIFIED');
  static const DeviceType DEVICE_TYPE_IOS = DeviceType._(1, _omitEnumNames ? '' : 'DEVICE_TYPE_IOS');
  static const DeviceType DEVICE_TYPE_ANDROID = DeviceType._(2, _omitEnumNames ? '' : 'DEVICE_TYPE_ANDROID');
  static const DeviceType DEVICE_TYPE_MACOSX = DeviceType._(3, _omitEnumNames ? '' : 'DEVICE_TYPE_MACOSX');
  static const DeviceType DEVICE_TYPE_WINDOWS = DeviceType._(4, _omitEnumNames ? '' : 'DEVICE_TYPE_WINDOWS');
  static const DeviceType DEVICE_TYPE_LINUX = DeviceType._(5, _omitEnumNames ? '' : 'DEVICE_TYPE_LINUX');
  static const DeviceType DEVICE_TYPE_MACOSX_ARM = DeviceType._(6, _omitEnumNames ? '' : 'DEVICE_TYPE_MACOSX_ARM');
  static const DeviceType DEVICE_TYPE_WINDOWS_ARM = DeviceType._(7, _omitEnumNames ? '' : 'DEVICE_TYPE_WINDOWS_ARM');
  static const DeviceType DEVICE_TYPE_LINUX_ARM = DeviceType._(8, _omitEnumNames ? '' : 'DEVICE_TYPE_LINUX_ARM');
  static const DeviceType DEVICE_TYPE_HARMONY = DeviceType._(9, _omitEnumNames ? '' : 'DEVICE_TYPE_HARMONY');

  static const $core.List<DeviceType> values = <DeviceType> [
    DEVICE_TYPE_UNSPECIFIED,
    DEVICE_TYPE_IOS,
    DEVICE_TYPE_ANDROID,
    DEVICE_TYPE_MACOSX,
    DEVICE_TYPE_WINDOWS,
    DEVICE_TYPE_LINUX,
    DEVICE_TYPE_MACOSX_ARM,
    DEVICE_TYPE_WINDOWS_ARM,
    DEVICE_TYPE_LINUX_ARM,
    DEVICE_TYPE_HARMONY,
  ];

  static final $core.List<DeviceType?> _byValue = $pb.ProtobufEnum.$_initByValueList(values, 9);
  static DeviceType? valueOf($core.int value) =>  value < 0 || value >= _byValue.length ? null : _byValue[value];

  const DeviceType._(super.value, super.name);
}

class Appid extends $pb.ProtobufEnum {
  static const Appid APPID_UNSPECIFIED = Appid._(0, _omitEnumNames ? '' : 'APPID_UNSPECIFIED');
  static const Appid APPID_CALORIE_SCAN = Appid._(1, _omitEnumNames ? '' : 'APPID_CALORIE_SCAN');

  static const $core.List<Appid> values = <Appid> [
    APPID_UNSPECIFIED,
    APPID_CALORIE_SCAN,
  ];

  static final $core.List<Appid?> _byValue = $pb.ProtobufEnum.$_initByValueList(values, 1);
  static Appid? valueOf($core.int value) =>  value < 0 || value >= _byValue.length ? null : _byValue[value];

  const Appid._(super.value, super.name);
}

class ChatType extends $pb.ProtobufEnum {
  static const ChatType CHAT_TYPE_UNSPECIFIED = ChatType._(0, _omitEnumNames ? '' : 'CHAT_TYPE_UNSPECIFIED');
  static const ChatType CHAT_TYPE_PRIVATE = ChatType._(1, _omitEnumNames ? '' : 'CHAT_TYPE_PRIVATE');
  static const ChatType CHAT_TYPE_GROUP = ChatType._(2, _omitEnumNames ? '' : 'CHAT_TYPE_GROUP');

  static const $core.List<ChatType> values = <ChatType> [
    CHAT_TYPE_UNSPECIFIED,
    CHAT_TYPE_PRIVATE,
    CHAT_TYPE_GROUP,
  ];

  static final $core.List<ChatType?> _byValue = $pb.ProtobufEnum.$_initByValueList(values, 2);
  static ChatType? valueOf($core.int value) =>  value < 0 || value >= _byValue.length ? null : _byValue[value];

  const ChatType._(super.value, super.name);
}

class Status extends $pb.ProtobufEnum {
  static const Status STATUS_UNSPECIFIED = Status._(0, _omitEnumNames ? '' : 'STATUS_UNSPECIFIED');
  static const Status STATUS_OK = Status._(1, _omitEnumNames ? '' : 'STATUS_OK');
  static const Status STATUS_BAD_REQUEST = Status._(2, _omitEnumNames ? '' : 'STATUS_BAD_REQUEST');
  static const Status STATUS_UNAUTHORIZED = Status._(3, _omitEnumNames ? '' : 'STATUS_UNAUTHORIZED');
  static const Status STATUS_FORBIDDEN = Status._(4, _omitEnumNames ? '' : 'STATUS_FORBIDDEN');
  static const Status STATUS_NOT_FOUND = Status._(5, _omitEnumNames ? '' : 'STATUS_NOT_FOUND');
  static const Status STATUS_CONFLICT = Status._(6, _omitEnumNames ? '' : 'STATUS_CONFLICT');
  static const Status STATUS_INTERNAL_ERROR = Status._(7, _omitEnumNames ? '' : 'STATUS_INTERNAL_ERROR');
  static const Status STATUS_SERVICE_UNAVAILABLE = Status._(8, _omitEnumNames ? '' : 'STATUS_SERVICE_UNAVAILABLE');

  static const $core.List<Status> values = <Status> [
    STATUS_UNSPECIFIED,
    STATUS_OK,
    STATUS_BAD_REQUEST,
    STATUS_UNAUTHORIZED,
    STATUS_FORBIDDEN,
    STATUS_NOT_FOUND,
    STATUS_CONFLICT,
    STATUS_INTERNAL_ERROR,
    STATUS_SERVICE_UNAVAILABLE,
  ];

  static final $core.List<Status?> _byValue = $pb.ProtobufEnum.$_initByValueList(values, 8);
  static Status? valueOf($core.int value) =>  value < 0 || value >= _byValue.length ? null : _byValue[value];

  const Status._(super.value, super.name);
}

class CommandID extends $pb.ProtobufEnum {
  static const CommandID COMMAND_ID_UNSPECIFIED = CommandID._(0, _omitEnumNames ? '' : 'COMMAND_ID_UNSPECIFIED');
  static const CommandID COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR = CommandID._(1, _omitEnumNames ? '' : 'COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR');
  static const CommandID COMMAND_ID_HEARTBEAT = CommandID._(2, _omitEnumNames ? '' : 'COMMAND_ID_HEARTBEAT');
  static const CommandID COMMAND_ID_TOO_MANY_REQUEST = CommandID._(3, _omitEnumNames ? '' : 'COMMAND_ID_TOO_MANY_REQUEST');

  static const $core.List<CommandID> values = <CommandID> [
    COMMAND_ID_UNSPECIFIED,
    COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR,
    COMMAND_ID_HEARTBEAT,
    COMMAND_ID_TOO_MANY_REQUEST,
  ];

  static final $core.List<CommandID?> _byValue = $pb.ProtobufEnum.$_initByValueList(values, 3);
  static CommandID? valueOf($core.int value) =>  value < 0 || value >= _byValue.length ? null : _byValue[value];

  const CommandID._(super.value, super.name);
}


const $core.bool _omitEnumNames = $core.bool.fromEnvironment('protobuf.omit_enum_names');
