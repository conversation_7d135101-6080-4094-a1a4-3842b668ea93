// This is a generated file - do not edit.
//
// Generated from api/common/v1/common.proto.

// @dart = 3.3

// ignore_for_file: annotate_overrides, camel_case_types, comment_references
// ignore_for_file: constant_identifier_names
// ignore_for_file: curly_braces_in_flow_control_structures
// ignore_for_file: deprecated_member_use_from_same_package, library_prefixes
// ignore_for_file: non_constant_identifier_names, unused_import

import 'dart:convert' as $convert;
import 'dart:core' as $core;
import 'dart:typed_data' as $typed_data;

@$core.Deprecated('Use deviceTypeDescriptor instead')
const DeviceType$json = {
  '1': 'DeviceType',
  '2': [
    {'1': 'DEVICE_TYPE_UNSPECIFIED', '2': 0},
    {'1': 'DEVICE_TYPE_IOS', '2': 1},
    {'1': 'DEVICE_TYPE_ANDROID', '2': 2},
    {'1': 'DEVICE_TYPE_MACOSX', '2': 3},
    {'1': 'DEVICE_TYPE_WINDOWS', '2': 4},
    {'1': 'DEVICE_TYPE_LINUX', '2': 5},
    {'1': 'DEVICE_TYPE_MACOSX_ARM', '2': 6},
    {'1': 'DEVICE_TYPE_WINDOWS_ARM', '2': 7},
    {'1': 'DEVICE_TYPE_LINUX_ARM', '2': 8},
    {'1': 'DEVICE_TYPE_HARMONY', '2': 9},
  ],
};

/// Descriptor for `DeviceType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List deviceTypeDescriptor = $convert.base64Decode(
    'CgpEZXZpY2VUeXBlEhsKF0RFVklDRV9UWVBFX1VOU1BFQ0lGSUVEEAASEwoPREVWSUNFX1RZUE'
    'VfSU9TEAESFwoTREVWSUNFX1RZUEVfQU5EUk9JRBACEhYKEkRFVklDRV9UWVBFX01BQ09TWBAD'
    'EhcKE0RFVklDRV9UWVBFX1dJTkRPV1MQBBIVChFERVZJQ0VfVFlQRV9MSU5VWBAFEhoKFkRFVk'
    'lDRV9UWVBFX01BQ09TWF9BUk0QBhIbChdERVZJQ0VfVFlQRV9XSU5ET1dTX0FSTRAHEhkKFURF'
    'VklDRV9UWVBFX0xJTlVYX0FSTRAIEhcKE0RFVklDRV9UWVBFX0hBUk1PTlkQCQ==');

@$core.Deprecated('Use appidDescriptor instead')
const Appid$json = {
  '1': 'Appid',
  '2': [
    {'1': 'APPID_UNSPECIFIED', '2': 0},
    {'1': 'APPID_CALORIE_SCAN', '2': 1},
  ],
};

/// Descriptor for `Appid`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List appidDescriptor = $convert.base64Decode(
    'CgVBcHBpZBIVChFBUFBJRF9VTlNQRUNJRklFRBAAEhYKEkFQUElEX0NBTE9SSUVfU0NBThAB');

@$core.Deprecated('Use chatTypeDescriptor instead')
const ChatType$json = {
  '1': 'ChatType',
  '2': [
    {'1': 'CHAT_TYPE_UNSPECIFIED', '2': 0},
    {'1': 'CHAT_TYPE_PRIVATE', '2': 1},
    {'1': 'CHAT_TYPE_GROUP', '2': 2},
  ],
};

/// Descriptor for `ChatType`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List chatTypeDescriptor = $convert.base64Decode(
    'CghDaGF0VHlwZRIZChVDSEFUX1RZUEVfVU5TUEVDSUZJRUQQABIVChFDSEFUX1RZUEVfUFJJVk'
    'FURRABEhMKD0NIQVRfVFlQRV9HUk9VUBAC');

@$core.Deprecated('Use statusDescriptor instead')
const Status$json = {
  '1': 'Status',
  '2': [
    {'1': 'STATUS_UNSPECIFIED', '2': 0},
    {'1': 'STATUS_OK', '2': 1},
    {'1': 'STATUS_BAD_REQUEST', '2': 2},
    {'1': 'STATUS_UNAUTHORIZED', '2': 3},
    {'1': 'STATUS_FORBIDDEN', '2': 4},
    {'1': 'STATUS_NOT_FOUND', '2': 5},
    {'1': 'STATUS_CONFLICT', '2': 6},
    {'1': 'STATUS_INTERNAL_ERROR', '2': 7},
    {'1': 'STATUS_SERVICE_UNAVAILABLE', '2': 8},
  ],
};

/// Descriptor for `Status`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List statusDescriptor = $convert.base64Decode(
    'CgZTdGF0dXMSFgoSU1RBVFVTX1VOU1BFQ0lGSUVEEAASDQoJU1RBVFVTX09LEAESFgoSU1RBVF'
    'VTX0JBRF9SRVFVRVNUEAISFwoTU1RBVFVTX1VOQVVUSE9SSVpFRBADEhQKEFNUQVRVU19GT1JC'
    'SURERU4QBBIUChBTVEFUVVNfTk9UX0ZPVU5EEAUSEwoPU1RBVFVTX0NPTkZMSUNUEAYSGQoVU1'
    'RBVFVTX0lOVEVSTkFMX0VSUk9SEAcSHgoaU1RBVFVTX1NFUlZJQ0VfVU5BVkFJTEFCTEUQCA==');

@$core.Deprecated('Use commandIDDescriptor instead')
const CommandID$json = {
  '1': 'CommandID',
  '2': [
    {'1': 'COMMAND_ID_UNSPECIFIED', '2': 0},
    {'1': 'COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR', '2': 1},
    {'1': 'COMMAND_ID_HEARTBEAT', '2': 2},
    {'1': 'COMMAND_ID_TOO_MANY_REQUEST', '2': 3},
  ],
};

/// Descriptor for `CommandID`. Decode as a `google.protobuf.EnumDescriptorProto`.
final $typed_data.Uint8List commandIDDescriptor = $convert.base64Decode(
    'CglDb21tYW5kSUQSGgoWQ09NTUFORF9JRF9VTlNQRUNJRklFRBAAEioKJkNPTU1BTkRfSURfVF'
    'JBTlNfREVDUllQVElPTl9EQVRBX0VSUk9SEAESGAoUQ09NTUFORF9JRF9IRUFSVEJFQVQQAhIf'
    'ChtDT01NQU5EX0lEX1RPT19NQU5ZX1JFUVVFU1QQAw==');

@$core.Deprecated('Use errorDetailDescriptor instead')
const ErrorDetail$json = {
  '1': 'ErrorDetail',
  '2': [
    {'1': 'error_code', '3': 1, '4': 1, '5': 5, '10': 'errorCode'},
    {'1': 'message', '3': 2, '4': 1, '5': 9, '10': 'message'},
  ],
};

/// Descriptor for `ErrorDetail`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List errorDetailDescriptor = $convert.base64Decode(
    'CgtFcnJvckRldGFpbBIdCgplcnJvcl9jb2RlGAEgASgFUgllcnJvckNvZGUSGAoHbWVzc2FnZR'
    'gCIAEoCVIHbWVzc2FnZQ==');

@$core.Deprecated('Use baseRequestDescriptor instead')
const BaseRequest$json = {
  '1': 'BaseRequest',
  '2': [
    {'1': 'auth_token', '3': 1, '4': 1, '5': 9, '10': 'authToken'},
    {'1': 'request_id', '3': 2, '4': 1, '5': 9, '10': 'requestId'},
    {'1': 'client_version', '3': 3, '4': 1, '5': 9, '10': 'clientVersion'},
    {'1': 'locale', '3': 4, '4': 1, '5': 9, '10': 'locale'},
    {'1': 'timestamp', '3': 5, '4': 1, '5': 3, '10': 'timestamp'},
  ],
};

/// Descriptor for `BaseRequest`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List baseRequestDescriptor = $convert.base64Decode(
    'CgtCYXNlUmVxdWVzdBIdCgphdXRoX3Rva2VuGAEgASgJUglhdXRoVG9rZW4SHQoKcmVxdWVzdF'
    '9pZBgCIAEoCVIJcmVxdWVzdElkEiUKDmNsaWVudF92ZXJzaW9uGAMgASgJUg1jbGllbnRWZXJz'
    'aW9uEhYKBmxvY2FsZRgEIAEoCVIGbG9jYWxlEhwKCXRpbWVzdGFtcBgFIAEoA1IJdGltZXN0YW'
    '1w');

@$core.Deprecated('Use baseResponseDescriptor instead')
const BaseResponse$json = {
  '1': 'BaseResponse',
  '2': [
    {'1': 'status', '3': 1, '4': 1, '5': 14, '6': '.api.common.v1.Status', '10': 'status'},
    {'1': 'request_id', '3': 2, '4': 1, '5': 9, '10': 'requestId'},
    {'1': 'trace_id', '3': 3, '4': 1, '5': 9, '10': 'traceId'},
    {'1': 'error_detail', '3': 4, '4': 1, '5': 11, '6': '.api.common.v1.ErrorDetail', '10': 'errorDetail'},
  ],
};

/// Descriptor for `BaseResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List baseResponseDescriptor = $convert.base64Decode(
    'CgxCYXNlUmVzcG9uc2USLQoGc3RhdHVzGAEgASgOMhUuYXBpLmNvbW1vbi52MS5TdGF0dXNSBn'
    'N0YXR1cxIdCgpyZXF1ZXN0X2lkGAIgASgJUglyZXF1ZXN0SWQSGQoIdHJhY2VfaWQYAyABKAlS'
    'B3RyYWNlSWQSPQoMZXJyb3JfZGV0YWlsGAQgASgLMhouYXBpLmNvbW1vbi52MS5FcnJvckRldG'
    'FpbFILZXJyb3JEZXRhaWw=');

@$core.Deprecated('Use commonResponseDescriptor instead')
const CommonResponse$json = {
  '1': 'CommonResponse',
  '2': [
    {'1': 'response', '3': 1, '4': 1, '5': 11, '6': '.api.common.v1.BaseResponse', '10': 'response'},
  ],
};

/// Descriptor for `CommonResponse`. Decode as a `google.protobuf.DescriptorProto`.
final $typed_data.Uint8List commonResponseDescriptor = $convert.base64Decode(
    'Cg5Db21tb25SZXNwb25zZRI3CghyZXNwb25zZRgBIAEoCzIbLmFwaS5jb21tb24udjEuQmFzZV'
    'Jlc3BvbnNlUghyZXNwb25zZQ==');

