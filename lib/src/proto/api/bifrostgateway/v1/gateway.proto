syntax = "proto3";

package api.bifrostgateway.v1;

import "api/common/v1/common.proto";

option go_package = "code.interestingsoft.com/shared/proto/gen/go/api/bifrostgateway";

service BifrostGatewayService {
  rpc PushMessage(PushMessageRequest) returns (PushMessageResponse);
}

message HandshakeRequest {
  bytes public_key = 1;
  int64 timestamp = 2;
  bytes nonce = 3;
  bytes digest = 4; // sha256(public_key + timestamp + nonce)
  api.common.v1.DeviceType device_type = 5;
  api.common.v1.Appid appid = 6;
  string app_version = 7;
}

message HandshakeResponse {
  api.common.v1.BaseResponse response = 1;
  bytes public_key = 2;
  int64 timestamp = 3;
  bytes nonce = 4;
  uint64 auth_key_id = 5;
  bytes digest = 6; // sha256(public_key + timestamp + nonce)
}

message TransferPayload {
  api.common.v1.CommandID command_id = 1;
  bytes data = 2;
  bytes token = 3;
}

message HeartbeatRequest {}

message HeartbeatResponse {}

message PushMessageRequest {
  repeated uint64 device_ids = 1;
  int32 cmd = 2;
  bytes payload = 3; // @gotags: mask:"payload"
}

message PushMessageResponse {
  api.common.v1.BaseResponse response = 1;
  repeated uint64 failed_device_ids = 2;
}

message RawPayload {
  bytes data = 1;
}
