syntax = "proto3";

package api.common.v1;

option go_package = "code.interestingsoft.com/shared/proto/gen/go/api/common/v1";

enum DeviceType {
  DEVICE_TYPE_UNSPECIFIED = 0;
  DEVICE_TYPE_IOS = 1;
  DEVICE_TYPE_ANDROID = 2;
  DEVICE_TYPE_MACOSX = 3;
  DEVICE_TYPE_WINDOWS = 4;
  DEVICE_TYPE_LINUX = 5;
  DEVICE_TYPE_MACOSX_ARM = 6;
  DEVICE_TYPE_WINDOWS_ARM = 7;
  DEVICE_TYPE_LINUX_ARM = 8;
  DEVICE_TYPE_HARMONY = 9;
}

enum Appid {
  APPID_UNSPECIFIED = 0;
  APPID_CALORIE_SCAN = 1;
}

enum ChatType {
  CHAT_TYPE_UNSPECIFIED = 0;
  CHAT_TYPE_PRIVATE = 1;
  CHAT_TYPE_GROUP = 2;
}

enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_OK = 1;
  STATUS_BAD_REQUEST = 2;
  STATUS_UNAUTHORIZED = 3;
  STATUS_FORBIDDEN = 4;
  STATUS_NOT_FOUND = 5;
  STATUS_CONFLICT = 6;
  STATUS_INTERNAL_ERROR = 7;
  STATUS_SERVICE_UNAVAILABLE = 8;
}

message ErrorDetail {
  int32 error_code = 1;
  string message = 2;
}

message BaseRequest {
  string auth_token = 1;
  string request_id = 2;
  string client_version = 3;
  string locale = 4;
  int64 timestamp = 5;
}

message BaseResponse {
  Status status = 1;
  string request_id = 2;
  string trace_id = 3;
  ErrorDetail error_detail = 4;
}

message CommonResponse {
  BaseResponse response = 1;
}

enum CommandID {
  COMMAND_ID_UNSPECIFIED = 0;
  COMMAND_ID_TRANS_DECRYPTION_DATA_ERROR = 0X0001;
  COMMAND_ID_HEARTBEAT = 0X0002;
  COMMAND_ID_TOO_MANY_REQUEST = 0X0003;
}
