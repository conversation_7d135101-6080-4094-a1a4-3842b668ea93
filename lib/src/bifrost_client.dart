import 'dart:typed_data';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:cryptography/cryptography.dart';
import 'package:fixnum/fixnum.dart';
import 'package:es_compression/lz4.dart';

import 'codec/packet.dart';
import 'codec/protocol_constants.dart';
import 'proto/gen/dart/api/bifrostgateway/v1/gateway.pb.dart' as bifrostgateway;
import 'proto/gen/dart/api/common/v1/common.pb.dart' as common;
import 'transport/bifrost_transport.dart';
import 'transport/bifrost_transport_proxy.dart';

class BifrostClient {
  final BifrostTransport transport;

  final common.DeviceType deviceType;
  final common.Appid appid;
  final String appVersion;

  Uint8List? _sharedSecret;
  int? _authKeyId;
  int _sequenceId = 0;
  DateTime? _keyEstablishedAt;

  SimpleKeyPair? _ecdhKeyPair;
  final Cipher _aes = AesGcm.with256bits();

  void Function(bifrostgateway.TransferPayload)? onMessage;

  BifrostClient({
    required String socketHost,
    required int socketPort,
    required String handshakeUrl,
    required String transferUrl,
    required this.deviceType,
    required this.appid,
    required this.appVersion,
  }) : transport = BifrostTransportProxy(
          socketHost: socketHost,
          socketPort: socketPort,
          handshakeUrl: handshakeUrl,
          transferUrl: transferUrl,
        );

  Future<void> connect() async {
    await transport.connect();
    await _performHandshake();
  }

  Future<void> _performHandshake() async {
    final algorithm = X25519();
    _ecdhKeyPair = await algorithm.newKeyPair();
    final publicKey = await _ecdhKeyPair!.extractPublicKey();

    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final nonce = _generateRandomBytes(16);
    final pubBytes = publicKey.bytes;

    final digest = sha256.convert([
      ...pubBytes,
      ..._int64ToBytes(timestamp),
      ...nonce,
    ]).bytes;

    final req = bifrostgateway.HandshakeRequest(
      publicKey: Uint8List.fromList(pubBytes),
      timestamp: Int64(timestamp),
      nonce: nonce,
      digest: digest,
      deviceType: deviceType,
      appid: appid,
      appVersion: appVersion,
    );

    final packet = HandshakePacket(
      magic: protocolMagic,
      version: protocolVersion,
      commandType: CommandType.exchangeKey,
      compressType: CompressType.none,
      payloadType: PayloadType.protobuf,
      payload: req.writeToBuffer(),
    );

    final resp = await transport.send(packet.encode());

    final handshakePacket = HandshakePacket.decodeStream(resp);
    final res = bifrostgateway.HandshakeResponse.fromBuffer(handshakePacket.payload);

    _authKeyId = res.authKeyId.toInt();
    _keyEstablishedAt = DateTime.now();

    final remotePub = SimplePublicKey(res.publicKey, type: KeyPairType.x25519);
    final shared = await algorithm.sharedSecretKey(
      keyPair: _ecdhKeyPair!,
      remotePublicKey: remotePub,
    );
    final derived = await shared.extractBytes();
    _sharedSecret = Uint8List.fromList(
      derived.sublist(0, 32),
    ); // 32 bytes AES-256 key

  Future<void> ensureKeyValid() async {
    if (_keyEstablishedAt == null ||
        DateTime.now().difference(_keyEstablishedAt!) > Duration(hours: 24)) {
      await _performHandshake();
    }
  }

  Future<void> sendTransferMessage(
    bifrostgateway.TransferPayload payload, {
    int compressType = CompressType.none,
  }) async {
    await ensureKeyValid();

    var encoded = payload.writeToBuffer();

    if (compressType == CompressType.lz4) {
      encoded = Uint8List.fromList(lz4.encode(encoded));
    }

    final nonce = _generateRandomBytes(12);
    final secretKey = SecretKey(_sharedSecret!);
    final encrypted = await _aes.encrypt(
      encoded,
      secretKey: secretKey,
      nonce: nonce,
    );

    final combinedPayload = Uint8List.fromList([
      ...nonce,
      ...encrypted.cipherText,
      ...encrypted.mac.bytes,
    ]);

    final packet = TransferPacket(
      magic: protocolMagic,
      version: protocolVersion,
      commandType: CommandType.clientRequest,
      compressType: compressType,
      payloadType: PayloadType.protobuf,
      authKeyId: _authKeyId!,
      sequenceId: _sequenceId++,
      payload: combinedPayload,
    );

    final responseBytes = await transport.send(packet.encode());
    final responsePacket = TransferPacket.decodeStream(responseBytes);

    await _handleTransferPacket(responsePacket);
  }

  Future<void> _handleTransferPacket(TransferPacket packet) async {
    try {
      if (packet.authKeyId == 0) {
        var payloadData = packet.payload;
        if (packet.compressType == CompressType.lz4) {
          payloadData = Uint8List.fromList(lz4.decode(payloadData));
        }
        final payload = bifrostgateway.TransferPayload.fromBuffer(payloadData);
        if (onMessage != null) onMessage!(payload);
        return;
      }

      final data = packet.payload;
      if (data.length < 12 + 16) return;

      final nonce = data.sublist(0, 12);
      final mac = data.sublist(data.length - 16);
      final cipherText = data.sublist(12, data.length - 16);

      final secretKey = SecretKey(_sharedSecret!);
      final decrypted = await _aes.decrypt(
        SecretBox(cipherText, nonce: nonce, mac: Mac(mac)),
        secretKey: secretKey,
      );

      var payloadData = Uint8List.fromList(decrypted);
      if (packet.compressType == CompressType.lz4) {
        payloadData = Uint8List.fromList(lz4.decode(payloadData));
      }

      final payload = bifrostgateway.TransferPayload.fromBuffer(payloadData);
      if (onMessage != null) onMessage!(payload);
    } catch (e) {
      print('Failed to decrypt or parse packet: $e');
    }
  }

  void close() {
    transport.disconnect();
  }

  Uint8List _generateRandomBytes(int length) {
    final random = Random.secure();
    return Uint8List.fromList(
      List.generate(length, (_) => random.nextInt(256)),
    );
  }

  Uint8List _int64ToBytes(int value) {
    final bytes = ByteData(8)..setInt64(0, value, Endian.big);
    return bytes.buffer.asUint8List();
  }
}
