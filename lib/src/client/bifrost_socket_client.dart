import 'dart:async';
import 'dart:io';
import 'dart:typed_data';

import '../codec/packet.dart';
import '../codec/protocol_constants.dart';

/// Result wrapper for decoding to carry both packet and remaining buffer
class DecodeResult {
  final Packet packet;
  final Uint8List remaining;

  DecodeResult(this.packet, this.remaining);
}

class BifrostSocketClient {
  final String host;
  final int port;

  Socket? _socket;
  final _receiveBuffer = BytesBuilder();

  Completer<Uint8List>? _responseCompleter;

  BifrostSocketClient({required this.host, required this.port});

  Future<void> connect() async {
    _socket = await Socket.connect(host, port);
    _socket?.listen(_onData, onError: _onError, onDone: _onDone);
  }

  /// Sends a packet and waits for the first matching response
  Future<Uint8List> send(Uint8List data) {
    final completer = Completer<Uint8List>();
    _responseCompleter = completer;

    _socket?.add(data);

    return completer.future.timeout(
      const Duration(seconds: 5),
      onTimeout: () {
        if (!completer.isCompleted) {
          completer.completeError(TimeoutException("Response timeout"));
        }
        return Uint8List(0);
      },
    );
  }

  void _onData(Uint8List data) {
    _receiveBuffer.add(data);

    while (true) {
      final buffer = _receiveBuffer.toBytes();

      // Try decode TransferPacket first (has more fields)
      Packet? packet;
      int consumedLength = 0;

      if (buffer.length < 4 + baseHeaderLength) {
        break;
      }

      final commandType = (buffer[5] >> 5) & 0x07;

      if (commandType == CommandType.exchangeKey) {
        final result = _tryDecodeHandshake(buffer);
        if (result == null) break;
        packet = result.packet;
        consumedLength = buffer.length - result.remaining.length;
      } else {
        final result = _tryDecodeTransfer(buffer);
        if (result == null) break;
        packet = result.packet;
        consumedLength = buffer.length - result.remaining.length;
      }

      // Remove consumed data
      _receiveBuffer.clear();
      _receiveBuffer.add(buffer.sublist(consumedLength));

      if (_responseCompleter != null && !_responseCompleter!.isCompleted) {
        _responseCompleter!.complete(packet.payload);
      }
    }
  }

  DecodeResult? _tryDecodeHandshake(Uint8List buffer) {
    final packet = HandshakePacket.decodeStream(buffer);
    if (packet == null) return null;

    final payloadLength = packet.payload.length;
    final totalLength = magicLength + baseHeaderLength + payloadLength;
    final remaining = buffer.sublist(totalLength);

    return DecodeResult(packet, remaining);
  }

  DecodeResult? _tryDecodeTransfer(Uint8List buffer) {
    final packet = TransferPacket.decodeStream(buffer);
    if (packet == null) return null;

    final payloadLength = packet.payload.length;
    final totalLength =
        magicLength + baseHeaderLength + transferExtraLength + payloadLength;
    final remaining = buffer.sublist(totalLength);

    return DecodeResult(packet, remaining);
  }

  void _onError(error) {
    _socket?.destroy();
    if (_responseCompleter != null && !_responseCompleter!.isCompleted) {
      _responseCompleter!.completeError(error);
    }
  }

  void _onDone() {
    _socket?.destroy();
  }

  void close() {
    _socket?.destroy();
  }
}
