import 'dart:typed_data';

abstract class BifrostTransport {
  /// For long connection: connect socket
  Future<void> connect();

  /// For long connection: disconnect socket
  Future<void> disconnect();

  /// Send raw bytes and get raw response bytes
  Future<Uint8List> send(Uint8List data);

  /// For long connection: receive data stream, optional for short connection
  Stream<Uint8List>? get onData;

  bool get isPersistentConnection;
}
