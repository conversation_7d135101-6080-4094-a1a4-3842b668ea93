// lib/src/transport/bifrost_transport_proxy.dart
import 'dart:async';
import 'dart:typed_data';
import 'package:mastiff_logger/mastiff_logger.dart';

import 'bifrost_transport.dart';
import 'bifrost_socket_transport.dart';
import 'bifrost_http_transport.dart';

class BifrostTransportProxy implements BifrostTransport {
  static final _logger = logger;

  final String _socketHost;
  final int _socketPort;
  final String _handshakeUrl;
  final String _transferUrl;

  BifrostTransport? _currentTransport;
  bool _usingSocket = true;
  bool _isConnected = false;

  StreamController<Uint8List>? _dataController;

  BifrostTransportProxy({
    required String socketHost,
    required int socketPort,
    required String handshakeUrl,
    required String transferUrl,
  }) : _socketHost = socketHost,
       _socketPort = socketPort,
       _handshakeUrl = handshakeUrl,
       _transferUrl = transferUrl;

  @override
  Future<void> connect() async {
    if (_isConnected) return;

    // 优先尝试 Socket 连接
    try {
      _logger.fine('Attempting socket connection...');
      final socketTransport = BifrostSocketTransport(_socketHost, _socketPort);
      await socketTransport.connect();

      _currentTransport = socketTransport;
      _usingSocket = true;
      _isConnected = true;
      _logger.info('Socket connection established');

      // 设置数据流监听
      _setupDataStream();
      return;
    } catch (e) {
      _logger.warning('Socket connection failed: $e');
    }

    // 回退到 HTTP
    _logger.fine('Falling back to HTTP transport');
    _currentTransport = BifrostHttpTransport(
      handshakeUrl: _handshakeUrl,
      transferUrl: _transferUrl,
    );
    _usingSocket = false;
    _isConnected = true;

    // HTTP 不需要连接操作
    _logger.info('Using HTTP transport');
  }

  void _setupDataStream() {
    if (_usingSocket) {
      _dataController = StreamController<Uint8List>.broadcast();
      _currentTransport!.onData?.listen(
        (data) => _dataController?.add(data),
        onError: (e) => _dataController?.addError(e),
        onDone: () => _dataController?.close(),
      );
    }
  }

  @override
  Future<void> disconnect() async {
    await _currentTransport?.disconnect();
    await _dataController?.close();
    _isConnected = false;
    _dataController = null;
  }

  @override
  Future<Uint8List> send(Uint8List data) async {
    if (!_isConnected) {
      await connect();
    }

    try {
      return await _currentTransport!.send(data);
    } catch (e) {
      _logger.warning('Send failed: $e');

      // 如果是 Socket 传输失败，切换到 HTTP 并重试
      if (_usingSocket) {
        _logger.fine('Switching to HTTP transport');
        await _currentTransport?.disconnect();

        _currentTransport = BifrostHttpTransport(
          handshakeUrl: _handshakeUrl,
          transferUrl: _transferUrl,
        );
        _usingSocket = false;

        return await _currentTransport!.send(data);
      }

      rethrow;
    }
  }

  @override
  Stream<Uint8List>? get onData =>
      _usingSocket ? _dataController?.stream : null;
}
