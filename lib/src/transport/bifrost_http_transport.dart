import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'bifrost_transport.dart';
import '../codec/protocol_constants.dart';

class BifrostHttpTransport implements BifrostTransport {
  final String handshakeUrl;
  final String transferUrl;

  @override
  bool get isPersistentConnection => false;

  BifrostHttpTransport({required this.handshakeUrl, required this.transferUrl});

  @override
  Future<void> connect() async {
    // 短连接不需要长连接建立
    return;
  }

  @override
  Future<void> disconnect() async {
    // 短连接不需要长连接关闭
    return;
  }

  @override
  Stream<Uint8List>? get onData => null;

  @override
  Future<Uint8List> send(Uint8List data) async {
    // 由于短连接协议，第一次调用必须是握手请求
    // 这里根据 data 结构判断调用哪个接口
    // 简单示例：如果是握手请求包，POST到 handshakeUrl，否则POST到 transferUrl

    final isHandshake = _isHandshakePacket(data);
    final url = isHandshake ? handshakeUrl : transferUrl;

    final response = await http.post(
      Uri.parse(url),
      headers: {'Content-Type': 'application/octet-stream'},
      body: data,
    );

    if (response.statusCode != 200) {
      throw Exception('HTTP Transport failed: ${response.statusCode}');
    }

    return response.bodyBytes;
  }

  bool _isHandshakePacket(Uint8List data) {
    if (data.length < 3) return false;

    final flags = data[2];
    final commandType = (flags >> 5) & 0x07;

    return commandType == CommandType.exchangeKey;
  }
}
