import 'dart:async';
import 'dart:typed_data';
import '../client/bifrost_socket_client.dart';
import 'bifrost_transport.dart';

class BifrostSocketTransport implements BifrostTransport {
  final String host;
  final int port;

  @override
  bool get isPersistentConnection => true;

  late BifrostSocketClient _socketClient;
  final StreamController<Uint8List> _dataController =
      StreamController.broadcast();

  BifrostSocketTransport(this.host, this.port) {
    _socketClient = BifrostSocketClient(host: host, port: port);
    _socketClient.onPacketReceived = (packet) {
      // 假设packet有 toBytes 方法返回 Uint8List
      _dataController.add(packet.toBytes());
    };
  }

  @override
  Future<void> connect() async {
    await _socketClient.connect();
  }

  @override
  Future<void> disconnect() async {
    await _socketClient.close();
    await _dataController.close();
  }

  @override
  Future<Uint8List> send(Uint8List data) async {
    // 这里假设sendAndWaitForResponse是你BifrostSocketClient里已有的方法
    final responseBytes = await _socketClient.send(data);
    return responseBytes;
  }

  @override
  Stream<Uint8List>? get onData => _dataController.stream;
}
