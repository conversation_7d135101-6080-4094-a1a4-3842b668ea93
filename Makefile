lint:
	@echo Lint start
	@flutter analyze

test:
	@echo Test start
	@export LD_LIBRARY_PATH=$(shell find $$HOME/.pub-cache/hosted/pub.flutter-io.cn/ -type f -name '*.so' -exec dirname {} \; | sort -u | tr '\n' ':')$$LD_LIBRARY_PATH; \
	flutter test --reporter expanded --coverage --test-randomize-ordering-seed random
	@lcov --summary coverage/lcov.info

cover: test
	@genhtml coverage/lcov.info -o coverage/html
	@open coverage/html/index.html

clean:
	@rm -f coverage/*
	@echo Clean Finish

.PHONY: lint test cover clean
